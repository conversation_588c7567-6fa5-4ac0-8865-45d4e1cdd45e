{"Records": [{"eventSource": "aws:ses", "eventVersion": "1.0", "ses": {"mail": {"timestamp": "2024-12-18T07:14:00.381Z", "source": "<EMAIL>", "messageId": "aq0hn48ekpqbbung91p8tge1s40avu5ee859h9g1", "destination": ["<EMAIL>"], "headersTruncated": "False", "headers": [{"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Received", "value": "from sonic309-21.consmr.mail.sg3.yahoo.com (sonic309-21.consmr.mail.sg3.yahoo.com [*************]) by inbound-smtp.us-east-1.amazonaws.com with SMTP id aq0hn48ekpqbbung91p8tge1s40avu5ee859h9g1 for <EMAIL>; Wed, 18 Dec 2024 07:14:00 +0000 (UTC)"}, {"name": "X-SES-Spam-Verdict", "value": "PASS"}, {"name": "X-SES-Virus-Verdict", "value": "PASS"}, {"name": "Received-SPF", "value": "pass (spfCheck: domain of _spf.mail.yahoo.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=sonic309-21.consmr.mail.sg3.yahoo.com;"}, {"name": "Authentication-Results", "value": "amazonses.com; spf=pass (spfCheck: domain of _spf.mail.yahoo.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=sonic309-21.consmr.mail.sg3.yahoo.com; dkim=pass header.i=@yahoo.com; dmarc=pass header.from=yahoo.com;"}, {"name": "X-SES-RECEIPT", "value": "AEFBQUFBQUFBQUFHQk9uYldZRURBVStoQmRrR3NzOVRBTDRSRk56eTQ3dzhvL0FHb1B2d01DaXlqZVFLRmpGZlEyZFV5NUN3OWJ1NXZSTlVxQmxBdVkzUnlsZnB3bGVTVDh3QkRvTXUvaEc1UkZZMXRyR2J1eVV1MlRPTmFuN1BQSlluUllnSkUwcjZCYXc5bmp6UEVQbldSWTJ5MFhhc0hHeDQxajJlTVNLb1BVV0sxbXROaVEySmJLOTYvQml2d2FWUmZwSnR2RHRkaGI4MkhBd204OFNwZ0RvaExXYjUrTHpBZTMzektCVm81eXFIZ2V2T3dwblhOcm5HQWdJTDhpUSt2eDJrczJhbmhJUWZPaHRJQklSZzNxMytNV08rby8vUGRpYUtFOWlGUmljY0VwaHBmOHc9PQ=="}, {"name": "X-SES-DKIM-SIGNATURE", "value": "a=rsa-sha256; q=dns/txt; b=qpLtBhmbuQ+sUmvdqYaiGYSlY4BtAhCTcHo1gzEfplePsl4wAk1bupAsibBZtRV1qI8pK2eZLwxVdo/yCKBRfuBy2XhCSFbc/PcieXDrFoE4FWCwtC2mPzHVDVk92zxOpiUla4PEyWL58506/f9diC8o04gydzVe0Yw/E8XExZw=; c=relaxed/simple; s=ug7nbtf4gccmlpwj322ax3p6ow6yfsug; d=amazonses.com; t=1734506041; v=1; bh=M2FDAM3wbiAdcWOS99Www4hGyZm3PPuI68eipUwqWvA=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;"}, {"name": "DKIM-Signature", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed; d=yahoo.com; s=s2048; t=1734506037; bh=bEoYipdFyw7eLNwsbhLS5KSxGlCAYKWMevTzPKWy+WI=; h=Date:From:To:In-Reply-To:References:Subject:From:Subject:Reply-To; b=Df/Gyjb37a1JW0qx+SkxkW6rKUDlyAT6j8CvFY0OBUhWNP/CdcphCsxvYpae3EC6KAc9BqXDbnf8j1xNMR/gluo6UkHwrLhrjjwmSphnCZiOUSkjjK60rDAmIOg1hzOOdl32LJsvfm2M8gbpON2SBrHk+pR5ykMBcB9819wVO/Dc6RfB7fBJvMsa06RErxx9yCRcIbYWJh3J55mpG8lch0yA+m+ovyz/I1TygbqHKytYN/Fn1wDJ8CjLDSpPYrpDIOA6nnmIQIonPHvDZe03Vy0K89lN0WydvNT7qdcJy4McnNbg2a5WIOM+WYyt54hsN3BI3zlbUCubcAKaduy4xg=="}, {"name": "X-SONIC-DKIM-SIGN", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed; d=yahoo.com; s=s2048; t=1734506037; bh=8HZSpKNFX186u926h8d9k1SN3H4IcaP67XcdJuCabeY=; h=X-Sonic-MF:Date:From:To:Subject:From:Subject; b=qINdIyQY/VJ1+QiZ/ofPfJGVGmwS+QYSuKB2Htiht3DD/I0Gb2yFuGRgPeioN0bfE9fMjUdhNxXg3ew2zBaR0NCGpQoTLLL+EcrmZzyqsexReLCfwUjW9bTOfGiITTLJq2WqxQbYUiZVZIdStRdIWkBiqcotW9uGOoUJCrL+s1J3oeCaaYZSjSVVEOo92Zwi0vu0qQjd5sR2tOn0iA6rrZoIZ4BmgJ31PsrUgUfchlWMfOYta2l3ZGjFN+YefC8H/D82rMYaU6dZTzmrr7fDugGOfUQmJLq7VGpRjPEGh6nZXg33oYTh8ExY9Pi5LjHg+T8SDTW18w9GaZdljud2zA=="}, {"name": "X-YMail-OSG", "value": "ah8KlMIVM1mLutOTN0fSA9xkM6QHz83qRqW_FOxFVrCsNXi4jXirAiLhnm8TcXO dSH0wLFYOiqb_WaFaupBTAOMMudmTSCpD20dMpQG3S1ySM__o_ignbiMDIxTmq1dRvT3Oz21fK8N QSeAX7ai7oHmZ2r1a_YclNfWmbPxdeHC3UhunwbmCow0DHo6E5bsL83r02z6s6N9SYMAv7VNEg1U oWNzxv8oNQVm17748NcyJhwDqf5C6oxeBf9I4XxW1EzZwS1hzIgUqXedH6NrQXGTslokykZMIWAf we0LtZ_FH0RDncOkPxzpC8fe_KxpVkANU7Z62gOsZ_qyTelynyy2sec3NWRchNmVPJL3J3CCkeUB Hyp2aVfnut2BRp5LxvY0n25hByoyf45_dIXTvXO45SYqNLbzPIHw3SMoIMQ_tjeHQGGoj3R4Vu66 _c9TMVSPuwszIpnfRPQDyQjQAqZfT46vBwHqbSXKLgX_NchZNc0Xw2s3vZSb5V9aFETH5fH9f6FM EDNgu1yVhpi2u7SQbQ.sirop1RHOQ1cYHbvisvnrfwoKV1Yo2YOSEfyOfMZcKYw4sEbeK3PeJcXY JI23HYcsRwLwOYWmUUGnYwDEW5luYn6vF.yFeF3ZuUDKtklI16Zfmt.JQFa09D9lsAHcY0pdFp2r 99DyiFnkutsJddZBbI.eQfiROKMMUcOTp1CAmOstPBkKDKh0W7TPgNvFp.J4wYcaErJ8ILCIlwwt sc6IOZhmNrFc8eCDPTizzSPIfljfTbytMewTcY8sQRtSBppR6c6wW9v4XCVLmoaZugB5aP1r156S 47WSl1hFfJF91X796Qfy_eIR8oSQLKToyztoCJL4mpB2Fv.PtyIwxgmUHtdIPrkQsXOx6fJgqAnP 77iCmTCX8HLzpeLta2PXJJ4B.EEo3g2pqjS6zb6lNR4whjWwdYW7e3dCF1FvbkQ1DsRfH2xWmzF3 IxczS55gWUfCKan7IMh2DjX0orSVqmY68ykXLDyDzGA5OS9CCqToftqCn3L7BQ5f2wKjWQI6Zm.l bZNmiGde.M59l3shlaMaskVpDPsSJD5QDsk5D0izRee51UHspWTa_nAOmAQxVveJPNQrAofO0cEO QIqZS.x0WJvOXXsNwFmbdwG_O42uVNEkW_Byo8VzA0ngz7Ivd224d73WR_7tJnz5TymmjAWS0IWy ZmvOwQmEcILDj4LcQvf8j1BNCVqcYbEICxgOvcwuR5b7p9yg3Z1gNCio_HR9PnYvPFpP9b6CWAoy b025GR9TvSq2DOa8wBiLrXqQ1UBE3zJ7Z9chK9WtGW29Gh0nvE83CsflwI4zFYv9LKPKZsorJ2ir vec6X_ktu3cV1wa3qRMy39D3iNNV58XQZRimL16TbBTAh9g5GF5ECSKiTVH1uDccClCfV_5x8p.. GwtsvaE3nWTtEkpNasy1zxOm9U2IIBYmrx3N8GgWdghWc_H7jFV1wAeVhyugDSXgH5775xtlWa26 Zz0dQgFSTnSpbx7GtK1Jz_q3Asaow3H9WzdynEBrd8b5K3TK0pfoYzDGnYyv7WFVJfkxTohyJDKE adOxMVZOibXdxhV1l31iSFOoJG.Xa1PBy1VDhZeNNGe1w2PGsXxX0YZHOKAMYxyO.9CDkey22EeU 98dLNFMAwKxf9PASOJhf_caSHjVjmaj5LMZ4tKscX8hyZQ4S3qLISjGyJB4V8RNFR6prv.rs5o9v Cb3OgLElptdBi2hB7s4wwByX1eaySy9ZwXPviL2tPrDVpI68sh7sBM0Uzc.uOa95jNGrpSv0cmck YuhJowQFKQv5iO9mG72ArelaBJ6wFVxWndeJMenNbb.29cZx8gZkJeq7oenSP3B744M.HlZKwUmz uGQ6xCxJNwSKVqlL4RO7KIt9CSzp5R5kuqpEGDVR7GUqoK83SsQw.g7oPAf8rdknF8UKd0k7pybp 7CzMiNZ7kzzEW.zDl30_JvKvnAojwrBpMCjcRQK8pe9_mM1KUBdzvQ5.nUZj4YTVeA7G6j6wmUHi 6Neix7NOyMCKYQskFQTbs3riN.gTgPIfYexS98YjmmC2gFsGuZzlcLsF5mhaRtvZUhXylC5ZvsgS gLPuGnDVBnlpchKtLiwK6fWsUAZkE82v7kD7neQEmUv1oc2yYDPzcf_lTYLKajMrKBhvnupW.hrF vOsN04QL46cwKYJC4VisAMGbep_.agLT2hakoEQn1VzRnotTa1kTWXQrpMVCuS5xqcMUVipLpElU dhxIk2.wm5psnzCrAdMAEM0OXGoSMPi_efwXgCbNazNWoyK.OTT4VJdpRVoUQYasC5881vmn1mup XCn111ZQdBegohRPLgJEG8DUNWG23pg--"}, {"name": "X-Sonic-MF", "value": "<<EMAIL>>"}, {"name": "X-Sonic-ID", "value": "bbf18fad-b451-41a3-bae4-e7104f72f70c"}, {"name": "Received", "value": "from sonic.gate.mail.ne1.yahoo.com by sonic309.consmr.mail.sg3.yahoo.com with HTTP; Wed, 18 Dec 2024 07:13:57 +0000"}, {"name": "Date", "value": "Wed, 18 Dec 2024 07:13:52 +0000 (UTC)"}, {"name": "From", "value": "j<PERSON><PERSON><PERSON><PERSON> nair <<EMAIL>>"}, {"name": "To", "value": "\"<EMAIL>\" <<EMAIL>>"}, {"name": "Message-ID", "value": "<<EMAIL>>"}, {"name": "In-Reply-To", "value": "<<EMAIL>>"}, {"name": "References", "value": "<<EMAIL>>"}, {"name": "Subject", "value": "Re: SES Email Receiving Test"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "Content-Type", "value": "multipart/alternative;  boundary=\"----=_Part_2850530_1392028428.1734506032584\""}, {"name": "X-Mailer", "value": "WebService/1.1.23040 YMailNorrin"}, {"name": "Content-Length", "value": "1375"}], "commonHeaders": {"returnPath": "<EMAIL>", "from": ["j<PERSON><PERSON><PERSON><PERSON> nair <<EMAIL>>"], "date": "Wed, 18 Dec 2024 07:13:52 +0000 (UTC)", "to": ["\"<EMAIL>\" <<EMAIL>>"], "messageId": "<<EMAIL>>", "subject": "Re: SES Email Receiving Test"}}, "receipt": {"timestamp": "2024-12-18T07:14:00.381Z", "processingTimeMillis": 1082, "recipients": ["<EMAIL>"], "spamVerdict": {"status": "PASS"}, "virusVerdict": {"status": "PASS"}, "spfVerdict": {"status": "PASS"}, "dkimVerdict": {"status": "PASS"}, "dmarcVerdict": {"status": "PASS"}, "action": {"type": "Lambda", "functionArn": "arn:aws:lambda:us-east-1:370841957185:function:coldemailer-email-forwarding", "invocationType": "Event"}}}}]}