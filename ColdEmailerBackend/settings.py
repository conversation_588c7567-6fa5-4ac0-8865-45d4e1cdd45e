"""
Django settings for ColdEmailerBackend project.

Generated by 'django-admin startproject' using Django 5.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""
import logging
import os
from datetime import timed<PERSON>ta
from logging import DEBUG
from pathlib import Path

import boto3
import botocore.client
import redis
import sentry_sdk
import stripe
from botocore.config import Config
from dotenv import load_dotenv
from google.oauth2 import service_account
from googleapiclient.discovery import build
from openai import OpenAI
from sentry_sdk.integrations.logging import LoggingIntegration

# Load environment variables
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# -------------------- Logger Setup --------------------
if os.environ["CE_DEBUG"] == "1":
    handlers = {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "colored",
        }
    }
    root = {
        "handlers": ["console"],
        "level": "WARNING",
    }
    loggers = {
        "dev": {
            "handlers": ["console"],
            "level": "DEBUG",
            "propagate": False,
        }
    }
else:
    handlers = {
        "error_file": {
            "class": "logging.FileHandler",
            "filename": os.environ["CE_LOG_ERROR_FILE_PATH"],
        }
    }
    root = {
        "handlers": ["error_file"],
        "level": "INFO",
    }
    loggers = {
        "gunicorn.error": {
            "handlers": ["error_file"],
            "level": "INFO",
            "propagate": True,
        },
    }

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "colored": {
            "()": "colorlog.ColoredFormatter",
            "format": "{log_color} {name} [{levelname}] - {message}",
            "style": "{",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
        }
    },
    "handlers": handlers,
    "root": root,
    "loggers": loggers,
}

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-sp8&yb@ce*gpq2tv-*5o1ck5$^(x+t0ee-2fb8%8ds!yau-e&b'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ["CE_DEBUG"] == "1"

ALLOWED_HOSTS = os.environ["CE_ALLOWED_HOSTS"].split(",")
CORS_ALLOWED_ORIGINS = os.environ["CE_CORS_ALLOWED_ORIGINS"].split(",")

# Sentry Monitoring (only for production)
if not DEBUG:
    sentry_logging = LoggingIntegration(
        level=logging.INFO,  # Capture this and above levels as breadcrumbs.
        event_level=logging.CRITICAL,  # Send these as events.
        sentry_logs_level=logging.INFO,
    )
    sentry_sdk.init(
        dsn="https://<EMAIL>/4509599351701504",
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=0.1,
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=0.1,
        # Add data like request headers and IP for users,
        # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
        send_default_pii=True,
        integrations=[
            sentry_logging,
        ],
        _experiments={
            "enable_logs": True,
        },
    )

# Application definition

INSTALLED_APPS = [
    'daphne',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'mainapp.apps.MainappConfig',
    'rest_framework',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'mainapp.middleware.UserActivityMiddleware',
]

# noinspection PyUnresolvedReferences
ROOT_URLCONF = 'ColdEmailerBackend.urls'

# We don't need templates for this Project since frontend is using React.
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    )
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'ROTATE_REFRESH_TOKENS': True,
    'USER_ID_FIELD': 'email',
    'SIGNING_KEY': os.environ['CE_JWT_SIGNING_KEY'],
}

# WSGI_APPLICATION = 'ColdEmailerBackend.wsgi.application'
ASGI_APPLICATION = "ColdEmailerBackend.asgi.application"
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [(os.environ["CE_REDIS_HOST"], int(os.environ["CE_REDIS_PORT"]))],
        },
    },
}

# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ["CE_POSTGRES_DB"],
        "USER": os.environ["CE_POSTGRES_USER"],
        "PASSWORD": os.environ["CE_POSTGRES_PASSWORD"],
        "HOST": os.environ["CE_POSTGRES_HOST"],
        "PORT": os.environ["CE_POSTGRES_PORT"],
    }
}

# Authentication
AUTH_USER_MODEL = 'mainapp.User'

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# AWS Boto3
# Make sure ~/.aws/credentials file is using correct values.
aws_ses = boto3.client("sesv2",
                       region_name=os.environ["CE_AWS_REGION"],
                       config=botocore.client.Config(
                           max_pool_connections=100,
                       ))
aws_route53 = boto3.client("route53",
                           region_name=os.environ["CE_AWS_REGION"],
                           config=botocore.client.Config(
                               retries={
                                   'max_attempts': 15,
                                   'mode': 'standard'
                               }
                           ))
aws_eventbridge = boto3.client("scheduler",
                               config=botocore.config.Config(
                                   max_pool_connections=100,
                                   retries={
                                       'max_attempts': 10,
                                       'mode': 'adaptive'
                                   }
                               ),
                               region_name=os.environ["CE_AWS_REGION"])
aws_s3_client = boto3.client("s3", region_name=os.environ["CE_AWS_REGION"])
aws_sns_client = boto3.client("sns", region_name=os.environ["CE_AWS_REGION"])
aws_cloudfront_client = boto3.client("cloudfront")
# ACM needs to be in us-east-1 for cloudfront.
aws_acm_client = boto3.client("acm", region_name="us-east-1")

# OpenAI
openai_client = OpenAI(
    api_key=os.environ["CE_OPENAI_API_KEY"],
)

# Celery Configuration Options
CELERY_BROKER_URL = os.environ["CE_CELERY_BROKER"]
CELERY_RESULT_BACKEND = os.environ["CE_CELERY_BACKEND"]
CELERY_TIMEZONE = "UTC"
CELERY_TASK_TRACK_STARTED = True
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = False  # During startup.
CELERY_BROKER_CONNECTION_RETRY = True  # After initial connection.

# Google Api
GOOGLE_API_SCOPES = [
    'https://www.googleapis.com/auth/postmaster.readonly'
]
google_api_credentials = service_account.Credentials.from_service_account_file(
    os.environ["CE_GOOGLE_API_SERVICE_ACCOUNT_FILE"], scopes=GOOGLE_API_SCOPES
)
google_api_service = build('siteVerification', 'v1', credentials=google_api_credentials)

# Stripe Config
stripe.api_key = os.environ["CE_STRIPE_API_KEY"]

# Warmup Settings
INITIAL_WARMUP_EMAIL_COUNT: float = 50.0
WARMUP_EMAILS_MAX: float = 40.0

# Campaign Settings.
INITIAL_DAILY_EMAIL_LIMIT = 200
DAILY_EMAIL_LIMIT_PERCENT_INCR_MIN = 4
DAILY_EMAIL_LIMIT_PERCENT_INCR_MAX = 8
MAX_DAILY_EMAIL_LIMIT_CAP = 500
DOMAIN_LIMIT_RESET_DAYS = 30
MAX_SENDING_LIMIT = 5000
CONTACT_LIST_MAX_ROWS = 100000
CONTACT_LIST_MAX_COLS = 20
