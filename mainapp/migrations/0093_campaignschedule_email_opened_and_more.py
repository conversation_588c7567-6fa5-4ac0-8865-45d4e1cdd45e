# Generated by Django 5.1.2 on 2025-09-01 14:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0092_campaignemailmessage_email_content_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='campaignschedule',
            name='email_opened',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='campaignschedule',
            name='email_opened_on',
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.CreateModel(
            name='EmailLinkClickEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('link', models.TextField()),
                ('clicked_on', models.DateTimeField()),
                ('schedule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaignschedule')),
            ],
        ),
    ]
