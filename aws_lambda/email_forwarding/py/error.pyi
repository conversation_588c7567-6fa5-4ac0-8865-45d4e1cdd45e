from typing import Any, Callable, TypeVar

_T = TypeVar('_T')

def checked_call(func: Callable[..., _T], *args: Any, **kwargs: Any) -> _T: ...
class Error(EnvironmentError): ...
class EPERM(Error): ...
class ENOENT(Error): ...
class ESRCH(Error): ...
class EINTR(Error): ...
class EIO(Error): ...
class ENXIO(Error): ...
class E2BIG(Error): ...
class ENOEXEC(Error): ...
class EBADF(Error): ...
class ECHILD(Error): ...
class EAGAIN(Error): ...
class ENOMEM(Error): ...
class EACCES(Error): ...
class EFAULT(Error): ...
class ENOTBLK(Error): ...
class EBUSY(Error): ...
class EEXIST(Error): ...
class EXDEV(Error): ...
class ENODEV(Error): ...
class ENOTDIR(Error): ...
class EISDIR(Error): ...
class EINVAL(Error): ...
class ENFILE(Error): ...
class EMFILE(Error): ...
class ENOTTY(Error): ...
class ETXTBSY(Error): ...
class EFBIG(Error): ...
class ENOSPC(Error): ...
class ESPIPE(Error): ...
class EROFS(Error): ...
class EMLINK(Error): ...
class EPIPE(Error): ...
class EDOM(Error): ...
class ERANGE(Error): ...
class EDEADLCK(Error): ...
class ENAMETOOLONG(Error): ...
class ENOLCK(Error): ...
class ENOSYS(Error): ...
class ENOTEMPTY(Error): ...
class ELOOP(Error): ...
class EWOULDBLOCK(Error): ...
class ENOMSG(Error): ...
class EIDRM(Error): ...
class ECHRNG(Error): ...
class EL2NSYNC(Error): ...
class EL3HLT(Error): ...
class EL3RST(Error): ...
class ELNRNG(Error): ...
class EUNATCH(Error): ...
class ENOCSI(Error): ...
class EL2HLT(Error): ...
class EBADE(Error): ...
class EBADR(Error): ...
class EXFULL(Error): ...
class ENOANO(Error): ...
class EBADRQC(Error): ...
class EBADSLT(Error): ...
class EDEADLOCK(Error): ...
class EBFONT(Error): ...
class ENOSTR(Error): ...
class ENODATA(Error): ...
class ETIME(Error): ...
class ENOSR(Error): ...
class ENONET(Error): ...
class ENOPKG(Error): ...
class EREMOTE(Error): ...
class ENOLINK(Error): ...
class EADV(Error): ...
class ESRMNT(Error): ...
class ECOMM(Error): ...
class EPROTO(Error): ...
class EMULTIHOP(Error): ...
class EDOTDOT(Error): ...
class EBADMSG(Error): ...
class EOVERFLOW(Error): ...
class ENOTUNIQ(Error): ...
class EBADFD(Error): ...
class EREMCHG(Error): ...
class ELIBACC(Error): ...
class ELIBBAD(Error): ...
class ELIBSCN(Error): ...
class ELIBMAX(Error): ...
class ELIBEXEC(Error): ...
class EILSEQ(Error): ...
class ERESTART(Error): ...
class ESTRPIPE(Error): ...
class EUSERS(Error): ...
class ENOTSOCK(Error): ...
class EDESTADDRREQ(Error): ...
class EMSGSIZE(Error): ...
class EPROTOTYPE(Error): ...
class ENOPROTOOPT(Error): ...
class EPROTONOSUPPORT(Error): ...
class ESOCKTNOSUPPORT(Error): ...
class ENOTSUP(Error): ...
class EOPNOTSUPP(Error): ...
class EPFNOSUPPORT(Error): ...
class EAFNOSUPPORT(Error): ...
class EADDRINUSE(Error): ...
class EADDRNOTAVAIL(Error): ...
class ENETDOWN(Error): ...
class ENETUNREACH(Error): ...
class ENETRESET(Error): ...
class ECONNABORTED(Error): ...
class ECONNRESET(Error): ...
class ENOBUFS(Error): ...
class EISCONN(Error): ...
class ENOTCONN(Error): ...
class ESHUTDOWN(Error): ...
class ETOOMANYREFS(Error): ...
class ETIMEDOUT(Error): ...
class ECONNREFUSED(Error): ...
class EHOSTDOWN(Error): ...
class EHOSTUNREACH(Error): ...
class EALREADY(Error): ...
class EINPROGRESS(Error): ...
class ESTALE(Error): ...
class EUCLEAN(Error): ...
class ENOTNAM(Error): ...
class ENAVAIL(Error): ...
class EISNAM(Error): ...
class EREMOTEIO(Error): ...
class EDQUOT(Error): ...
