import os
from datetime import datetime

from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.email_messages import domain_connection_reminder_email_message
from mainapp.models import User
from mainapp.tasks import send_email_task


class Command(BaseCommand):
    def handle(self, *args, **options):
        for user in User.objects.filter(has_sent_domain_connect_reminder_email=False):
            if (timezone.now() - user.date_joined).days >= 1:
                send_email_task.delay(
                    to=user.email,
                    sender="<EMAIL>",
                    sender_name="Junaid Ansari",
                    subject=f"Still need help connecting your domain, {user.username}?",
                    body_html=domain_connection_reminder_email_message(
                        username=user.username,
                        connect_domain_url=os.environ["CE_APP_HOST_URL"] + "/email-accounts/connect",
                        current_year=str(datetime.now().year),
                    )
                )
                user.has_sent_domain_connect_reminder_email = True
                user.save()
                self.stdout.write(self.style.SUCCESS(f"[*] Eamil task created for {user.email}"))

        self.stdout.write("All Done!")
