from typing import Dict

from django.core.management import BaseCommand

from ColdEmailerBackend.settings import aws_cloudfront_client, aws_acm_client
from mainapp.models import ManagedSubdomain

"""
Management command to find if CloudFront distribution for a given "deleting" status ManagedSubdomain entry has been 
disabled and if so, delete the distribution along with associated ACM certificate.
"""


class Command(BaseCommand):
    def handle(self, *args, **options):
        for msub in ManagedSubdomain.objects.filter(status="deleting"):
            self.stdout.write(self.style.SUCCESS(f"Running for {msub.subdomain}..."))
            # If CloudFront id is present, check if has been disabled and delete it along with the certificate
            # and connected domain.
            if msub.tracking_cf_id:
                cf_distr: Dict = aws_cloudfront_client.get_distribution(Id=msub.tracking_cf_id)
                enabled: bool = cf_distr["Distribution"]["DistributionConfig"]["Enabled"]
                etag: str = cf_distr["ETag"]
                if not enabled:
                    # Delete cloudfront distr.
                    self.stdout.write(self.style.SUCCESS(f"Deleting CloudFront Distribution {msub.tracking_cf_id}..."))
                    try:
                        aws_cloudfront_client.delete_distribution(
                            Id=msub.tracking_cf_id,
                            IfMatch=etag,
                        )
                    except Exception as err:
                        # Skip to next domain.
                        self.stdout.write(self.style.ERROR(f"{err}"))
                        continue

                    # Delete cert
                    if msub.tracking_acm_cert_arn:
                        self.stdout.write(self.style.SUCCESS(f"Deleting Certificate {msub.tracking_acm_cert_arn}..."))
                        aws_acm_client.delete_certificate(CertificateArn=msub.tracking_acm_cert_arn)
                    else:
                        self.stdout.write(self.style.WARNING(f"No ACM certificate found for {msub.subdomain}."))

                    # Delete connected domain
                    msub.delete()

                else:
                    self.stdout.write(self.style.NOTICE(f"CloudFront not yet disabled for {msub.subdomain}"))

            # If CloudFront id is missing, check if certificate is present and delete that + connected domain.
            else:
                self.stdout.write(self.style.WARNING(f"No CF id found for {msub.subdomain}. Deleting any issued ACM "
                                                     f"certificate and the connected domain."))
                # Delete cert
                if msub.tracking_acm_cert_arn:
                    self.stdout.write(self.style.SUCCESS(f"Deleting Certificate {msub.tracking_acm_cert_arn}..."))
                    aws_acm_client.delete_certificate(CertificateArn=msub.tracking_acm_cert_arn)
                else:
                    self.stdout.write(self.style.WARNING(f"No ACM certificate found for {msub.subdomain}."))

                # Delete connected domain
                msub.delete()

        self.stdout.write(self.style.SUCCESS("All Done!"))
