{"classifiers": ["Development Status :: 4 - Beta", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: Apache Software License", "Natural Language :: English", "Operating System :: OS Independent", "Programming Language :: Python", "Programming Language :: Python :: 2.6", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: Implementation :: PyPy", "Topic :: Software Development"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "invl", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/invl/retry"}}}, "extras": [], "generator": "bdist_wheel (0.29.0)", "license": "Apache License 2.0", "metadata_version": "2.0", "name": "retry", "run_requires": [{"requires": ["decorator (>=3.4.2)", "py (<2.0.0,>=1.4.26)"]}], "summary": "Easy to use retry decorator.", "test_requires": [{"requires": ["mock", "pbr", "pytest", "tox", "wheel"]}], "version": "0.9.2"}