import {<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rid<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alogT<PERSON>le, DialogContent, Autocomplete, Text<PERSON>ield, <PERSON><PERSON>, DialogActions} from "@mui/material";

import IconButton from "@mui/material/IconButton";
import {
	AlternateEmail,
	ArrowBack,
	Campaign,
	Cancel,
	ChevronRight,
	DomainVerification,
	FolderSpecial,
	Language,
	Send,
	CreditScore,
	Block,
	Add,
	Close,
	Delete,
} from "@mui/icons-material";
import {Link, useNavigate, useParams} from "react-router-dom";
import StatCard from "@components/StatCard";
import {useEffect, useState} from "react";
import {urls} from "@routes";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {useSnackbar} from "notistack";
import {useDialogs} from "@toolpad/core";

interface WorkspaceDetail {
	id: number;
	name: string;
}

interface PageData {
	status_code: number
	status_text: string

	email: string
	username: string
	user_id: number

	total_workspaces: number
	total_connected_domains: number
	total_email_subdomains: number
	total_email_ids: number
	total_campaigns: number
	total_emails_sent: number
	monthly_total_emails_remaining: number
	user_verified: boolean
	cancelled_due_to_bounce_count: number
	workspace_details: WorkspaceDetail[]

	campaigns_blocked: boolean
}

export default function AdminUserDetails() {
	const {userId} = useParams();
	const navigate = useNavigate();
	const {enqueueSnackbar} = useSnackbar();
	const dialogs = useDialogs();

	const [pageData, setPageData] = useState<PageData>();
	const [openAddCredit, setOpenAddCredit] = useState(false)
	const [workspace, setWorkspaces] = useState<WorkspaceDetail | null>(null);

	// Query to fetch user details.
	const pageDataQuery = useQuery({
		queryKey: ["adminUserDetailsPageDataQuery", userId],
		queryFn: () => authenticateAndFetchData(`/admin/get-user-details/?user_id=${userId}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data);
		}
	}, [pageDataQuery.data]);

	// Mutation to block campaigns.
	const blockCampaignMutation = useMutation({
		mutationKey: ["blockCampaignMutation", userId],
		mutationFn: () => authenticateAndPostData("/admin/block-user-campaigns/", {
			"id": userId
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			if (pageData) {
				setPageData({...pageData, campaigns_blocked: true})
			}
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Mutation to unblock campaigns.
	const unblockCampaignMutation = useMutation({
		mutationKey: ["unblockCampaignMutation", userId],
		mutationFn: () => authenticateAndPostData("/admin/unblock-user-campaigns/", {
			"id": userId
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			if (pageData) {
				setPageData({...pageData, campaigns_blocked: false})
			}
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Mutation to Verify user.
	const verifyUserMutation = useMutation({
		mutationKey: ["verifyUserMutation"],
		mutationFn: ({ verify_email }: { verify_email: boolean }) =>
			authenticateAndPostData("/admin/admin-user-verify-email/", {
				verify_email,
			}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			if (pageData) {
				setPageData({ ...pageData, user_verified: !pageData.user_verified });
			}
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});
	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => navigate(-1)}><ArrowBack/></IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>{pageData.username} ({pageData.email})</Typography>
				</Stack>
				<Divider/>
				<Stack direction={"column"} spacing={4}>
					{/* ---------------- Stat Cards ---------------- */}
					<Grid2 container spacing={3}>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<FolderSpecial/>}
								heading="Workspaces"
								value={pageData.total_workspaces}
								color="#3f51b5"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<DomainVerification/>}
								heading="Connected Domains"
								value={pageData.total_connected_domains}
								color="#2196f3"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<Language/>}
								heading="Email Subdomains"
								value={pageData.total_email_subdomains}
								color="#009688"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<AlternateEmail/>}
								heading="Email IDs"
								value={pageData.total_email_ids}
								color="#673ab7"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<Campaign/>}
								heading="Campaigns"
								value={pageData.total_campaigns}
								color="#ff5722"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<Send/>}
								heading="Emails Sent"
								value={pageData.total_emails_sent}
								color="#4caf50"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<CreditScore/>}
								heading="Total Email Credits Remaining"
								value={pageData.monthly_total_emails_remaining}
								color="#4caf50"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<Block />}
								heading="Total Bounce Campaigns"
								value={pageData.cancelled_due_to_bounce_count}
								color="#f44336"
							/>
						</Grid2>
					</Grid2>

					{/* ---------------- Workspace, Domain, etc. links ---------------- */}
					<Box display={"flex"} flexDirection={"column"} justifyContent={"start"} alignItems={"start"}>
						<Typography variant={"h6"}>View All Data:</Typography>
						<Stack direction={"row"} spacing={2} sx={{mt: 1}}>
							<Button variant={"outlined"}
											component={Link}
											to={urls["adminUserWorkspaces"].replace(":userId", userId || "")}
											endIcon={<ChevronRight/>}>
								Workspaces
							</Button>
							<Button variant={"outlined"}
											component={Link}
											to={urls["adminUserConnectedDomains"].replace(":userId", userId || "")}
											endIcon={<ChevronRight/>}>
								Connected Domains
							</Button>
							<Button variant={"outlined"}
											component={Link}
											to={urls["adminUserEmails"].replace(":userId", userId || "")}
											endIcon={<ChevronRight/>}>
								Emails
							</Button>
							<Button variant={"outlined"}
											component={Link}
											to={urls["adminUserCampaigns"].replace(":userId", userId || "")}
											endIcon={<ChevronRight/>}>
								Campaigns
							</Button>
						</Stack>
					</Box>

					{/* Block Campaigns */}
					<Box display={"flex"} flexDirection={"column"} justifyContent={"start"} alignItems={"start"}>
						<Typography variant={"h6"}>
							Block/Unblock Campaigns For This User:
						</Typography>
						<Typography variant={"body2"}>
							<b>Note</b>: Campaign cancellation takes place in backround. It might
							take some time based on how many schedules need to be deleted.
						</Typography>
						{pageData.campaigns_blocked ?
							<Button variant={"contained"}
											color={"success"}
											startIcon={<Cancel/>}
											sx={{mt: 1}}
											onClick={async () => {
												const unblockConfirmed = await dialogs.confirm(
													`Remove campaign creation restriction from this account?`,
													{
														title: "Unblock Campaigns",
														cancelText: "No",
														okText: "Yes",
													}
												);
												if (unblockConfirmed) {
													unblockCampaignMutation.mutate();
												}
											}}>
								Unblock Campaigns
							</Button> :
							<Button variant={"contained"}
											color={"error"}
											startIcon={<Cancel/>}
											sx={{mt: 1}}
											onClick={async () => {
												const blockConfirmed = await dialogs.confirm(
													`Are you sure you want to block campaigns for this user? All campaigns that haven't 
													finished yet will be cancelled and  they will not be able to create any new campaign. 
													Campaign cancellations cannot be undone. Proceed?`,
													{
														title: "Block Campaigns",
														cancelText: "No",
														okText: "Proceed",
													}
												);
												if (blockConfirmed) {
													blockCampaignMutation.mutate();
												}
											}}>
								Block Campaigns
							</Button>}
					</Box>

					{/* Block Campaigns */}
					<Box display={"flex"} flexDirection={"column"} justifyContent={"start"} alignItems={"start"}>
						<Typography variant={"h6"}>
							Verify User Email:
						</Typography>						
						{pageData.user_verified ? (
								<Button
									variant="contained"
									color="error"
									startIcon={<Cancel />}
									sx={{ mt: 1 }}
									onClick={() => {
										verifyUserMutation.mutate({ verify_email: false });
									}}
								>
									Unverify User Email
								</Button>
							) : (
								<Button
									variant="contained"
									color="primary"
									// startIcon={<Cancel />}
									sx={{ mt: 1 }}
									onClick={() => {
										verifyUserMutation.mutate({ verify_email: true });
									}}
								>
									Verify User Email
								</Button>
							)}
					</Box>
					
					<Box display={"flex"} flexDirection={"column"} justifyContent={"start"} alignItems={"start"}>
						<Typography variant={"h6"}>
							Add Email Credit:
						</Typography>					
					<Button variant={"contained"}
									size={"small"}
									startIcon={<Add/>}
									disabled={false}
									onClick={() => {
										setOpenAddCredit(true)										
									}}>
						Add Credit
					</Button>
					</Box>
					<Divider/>

				</Stack>
					<AddCreditPopup open={openAddCredit}
					userId={userId!}
					workspaceDetails={pageData.workspace_details}
					workspace={workspace}					
					onClose={() => setOpenAddCredit(false)}
					onSuccess={() => {
						setOpenAddCredit(false);
						pageDataQuery.refetch().then()
				}}/>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}

function AddCreditPopup(props: {
    open: boolean,
	userId: string
    workspaceDetails: WorkspaceDetail[],
    workspace: WorkspaceDetail | null,    
    onClose: () => void,
    onSuccess: () => void,
}) {	
	const {enqueueSnackbar} = useSnackbar();

	const [workspace, setWorkspaces] = useState<WorkspaceDetail | null>(null);
	const [credits, setCredits] = useState<number | null>(null)

	
	const AddRemoveCreditMutation = useMutation({
		mutationKey: ["addRemoveCreditMutation"],
		mutationFn: ({ addRemove }: { addRemove: string }) =>
			authenticateAndPostData("/admin/admin-add-remove-email-credit/", {
				workspaceId: workspace?.id,
				credits: credits,
				addRemove: addRemove,
			}),
			gcTime: 0,
			retry: retryFn,
			onSuccess: (_data, variables) => {
				enqueueSnackbar(`Credits ${variables.addRemove == "Add" ? 'added' : 'removed'} Successfully`, {
					variant: "success",
				});
				setCredits(null)
				setWorkspaces(null)
				props.onSuccess()
			},
			onError: (error: ApiRequestFailed) => {
				console.error(error);
				enqueueSnackbar(error.data.message, {
					variant: "error",
				});
			}
		});

		const handleAddRemoveCredits = (addRemove: string) => {			
			AddRemoveCreditMutation.mutate({ addRemove })
		}

		const handleWorkspaceSelect = (newValue: WorkspaceDetail | null) => {
			setWorkspaces(newValue);        
		};
		
		return (
        <Dialog 
            open={props.open} 
            onClose={props.onClose}      
			maxWidth="sm"
        >
            <DialogTitle>Add Or Remove Email Credit</DialogTitle>
			<DialogContent>
			<Stack direction={"column"} spacing={1}>
			<Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
				Select workspace
			</Typography>								
            <Autocomplete
                options={props.workspaceDetails}
                value={workspace}
                onChange={(_, newValue) => handleWorkspaceSelect(newValue)}
                getOptionLabel={(option) => option.name}
                renderInput={(params) => (
                    <TextField {...params} label="Workspace" />
                )}                
                PopperComponent={(popperProps) => (
					<Popper {...popperProps} style={{ zIndex: 2000 }} />
				)}
            />
			<Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>				
				Enter No of Credit
			</Typography>
			<TextField variant="outlined"
					size={"small"}
					value={credits}
					sx={{maxWidth: 500}}
					onChange={(e) => setCredits(Number(e.target.value))}/>
			{/* <Stack direction={"row"} spacing={2} alignItems={"center"}> */}
			<DialogActions sx={{ px: 3, pb: 2 }}>
				<Button variant="contained" color={"primary"} size={"small"} startIcon={<Add/>} onClick={() => {
								handleAddRemoveCredits("Add");
							}} disabled={!workspace || !credits}>
								Add
				</Button>
				<Button variant="contained" color={"error"} size={"small"} startIcon={<Delete/>} onClick={() => {
								handleAddRemoveCredits("Remove");
							}} disabled={!workspace || !credits}>
								Remove
				</Button>
				<Button variant="contained" color={"inherit"} size={"small"} startIcon={<Close/>} onClick={() => {
								setCredits(null); setWorkspaces(null) ;props.onClose();
							}}>
								Close
				</Button>
				</DialogActions>
			{/* </Stack> */}
			</Stack>
			</DialogContent>
        </Dialog>
    );
}
