import email.parser
import email.utils
import os
import re
from email.message import Message
from typing import Dict, List

import boto3

import mailparser
import requests
from retry import retry

s3_client = boto3.client('s3')
ses_client = boto3.client('sesv2')


def lambda_handler(event, context):
    print(event)

    from_addr_name: str = email.utils.parseaddr(event["Records"][0]["ses"]["mail"]["commonHeaders"]["from"][0])[0]
    from_addr_email: str = email.utils.parseaddr(event["Records"][0]["ses"]["mail"]["commonHeaders"]["from"][0])[1]
    to_addr_email: str = email.utils.parseaddr(event["Records"][0]["ses"]["mail"]["commonHeaders"]["to"][0])[1]
    subject: str = event["Records"][0]["ses"]["mail"]["commonHeaders"]["subject"]
    email_s3_key: str = event["Records"][0]["ses"]["mail"]["messageId"]
    headers: list[Dict] = event["Records"][0]["ses"]["mail"]["headers"]

    print(from_addr_name)
    print(from_addr_email)
    print(to_addr_email)
    print(subject)
    print(email_s3_key)

    # Check if this is a reply or failed delivery message.
    # if (from_addr_email == os.environ["aws_mailer_daemon"]) and (subject == "Delivery Status Notification (Failure)"):
    # if from_addr_email == os.environ["aws_mailer_daemon"]:
    if "MAILER-DAEMON" in from_addr_email:
        return_data = bounce_handler(email_s3_key)
    else:
        return_data = reply_handler(email_s3_key, from_addr_email, from_addr_name, headers, subject, to_addr_email)

    return return_data


def reply_handler(email_s3_key, from_addr_email, from_addr_name, headers, subject, to_addr_email):
    """
    Handles actual replies for cmapaign emails.

    :param email_s3_key:
    :param from_addr_email:
    :param from_addr_name:
    :param headers:
    :param subject:
    :param to_addr_email:
    :return:
    """
    # Check the from email id to filter out warmup emails.
    warmup_emails: List[str] = get_warmup_emails()
    if from_addr_email in warmup_emails:
        print(f"Email from {from_addr_email} is a warmup email. Skipping.")
        return {
            'statusCode': 200,
            'body': f"Email from {from_addr_email} is a warmup email. Skipping."
        }

    # Fetch the In-Reply-To message id.
    # If this is missing or has a bad value, we'll simply forward this email to respective user.
    filtered_headers: List[Dict] = list(filter(lambda h: h["name"] == "In-Reply-To", headers))
    if len(filtered_headers) > 0:
        in_reply_to_dict: Dict = filtered_headers[0]
        in_reply_to: str = extract_message_id(in_reply_to_dict["value"])
        if not in_reply_to:
            return simple_forwarding(email_s3_key, from_addr_name, from_addr_email, subject, to_addr_email)
        else:
            return campaign_reply(in_reply_to, email_s3_key, from_addr_name, from_addr_email, subject, to_addr_email)
    else:
        return simple_forwarding(email_s3_key, from_addr_name, from_addr_email, subject, to_addr_email)


def bounce_handler(email_s3_key):
    """
    Handles failed / bounced emails.
    """
    print("Marking as bounced...")

    # Fetch the email body from S3
    response = s3_client.get_object(
        Bucket=os.environ["bucket_name"],
        Key=email_s3_key
    )
    streaming_body = response["Body"]
    content = streaming_body.read()
    text_content: str = content.decode('utf-8')

    # Find the MIME boundary value, split the message body using it and find the original TO address
    # in the 2nd last part.
    boundary_pattern = r'boundary="([^"]+)"'
    mail = mailparser.parse_from_string(text_content)
    content_type: str = mail.headers['Content-Type']
    print("Content-Type: ", content_type)
    boundary_value_match = re.search(boundary_pattern, content_type)
    if boundary_value_match:
        boundary_value: str = boundary_value_match.group(1)
        to_address_pattern = r'^To:\s*([^\s]+)'

        raw_email_text: str = mail.message_as_string

        old_message_data: str = raw_email_text.split(boundary_value)[-2]
        to_address_match = re.search(to_address_pattern, old_message_data, re.MULTILINE)
        if to_address_match:
            original_to_address: str = to_address_match.group(1)
        else:
            raise Exception("bounce_handler() - No Message ID match found in message data.")

        # Remove quotes if present.
        original_to_address = original_to_address.strip("\"")

        # Find the reason otherwise add a generic message.
        diagnostic_code_pattern = r"Diagnostic-Code:\s*(.*)"
        diagnostic_data: str = raw_email_text.split(boundary_value)[-3]
        diagnostic_code_match = re.search(diagnostic_code_pattern, diagnostic_data, re.MULTILINE)
        reason: str = diagnostic_code_match.group(1) if diagnostic_code_match else "Message delivery failed."

        # Make API request to add this email address to bad email list
        print(f"Sending request to mark {original_to_address} as a bad email / bounce...")
        mark_bad_email(email_id=original_to_address, reason=reason, bounce=True)

        print("All done for bounce handling!")

    else:
        raise Exception("bounce_handler() - No boundary match found in message data.")

    return {
        'statusCode': 200,
        'body': original_to_address
    }


def campaign_reply(in_reply_to: str, email_s3_key: str, from_addr_name: str, from_addr_email: str, subject: str,
                   to_addr_email: str) -> Dict:
    """
    Handles campaign reply forwarding.
    """
    # Fetch the email body from S3. If no body is found, skip.
    body_text: str | None = get_email_body(email_s3_key)
    if body_text is None:
        return {
            'statusCode': 200,
            'body': "Could not find any plain text or html content in email. Skipping."
        }

    # Make API call to fetch user's destination email id.
    dest_email: str = fetch_user_destination_address(reply_to_message_id=in_reply_to)

    # Send a new email to user's destination email id. The TO address will become the new FROM address.
    # Additionally we'll keep the sender name as the original sender.
    response = ses_client.send_email(
        FromEmailAddress=to_addr_email,
        Destination={'ToAddresses': [
            dest_email
        ]},
        ReplyToAddresses=[
            f"{from_addr_name} <{from_addr_email}>",
        ],
        Content={
            'Simple': {
                'Subject': {'Data': subject},
                'Body': {
                    'Text': {'Data': body_text},
                },
            }
        },
    )
    message_id: str = response["MessageId"]
    print(f"Campaign message forwarded successfully to {dest_email}! Message ID: {message_id}")

    return {
        'statusCode': 200,
        'body': message_id
    }


def simple_forwarding(email_s3_key: str, from_addr_name: str, from_addr_email: str, subject: str, to_addr_email: str) -> Dict:
    """
    Handles other non-replies or new emails to a campaign email address.
    """
    # Fetch the email body from S3. If no body is found, skip.
    body_text: str | None = get_email_body(email_s3_key)
    if body_text is None:
        return {
            'statusCode': 200,
            'body': "Could not find any plain text or html content in email. Skipping."
        }

    # Fetch the forwarding address.
    forwarding_email_id: str = get_campaign_account_email(to_addr_email)

    # Simply forward this email to above address.
    response = ses_client.send_email(
        FromEmailAddress=to_addr_email,
        Destination={'ToAddresses': [
            f"<{forwarding_email_id}>"
        ]},
        ReplyToAddresses=[
            f"{from_addr_name} <{from_addr_email}>",
        ],
        Content={
            'Simple': {
                'Subject': {'Data': subject},
                'Body': {
                    'Text': {'Data': body_text},
                },
            }
        },
    )
    message_id: str = response["MessageId"]
    print(f"Message forwarded successfully to {forwarding_email_id}! Message ID: {message_id}")

    return {
        'statusCode': 200,
        'body': message_id
    }


# =======================================================================
# -------------------------- UTILITY FUNCTIONS --------------------------
# =======================================================================

@retry(requests.exceptions.Timeout, tries=3, delay=5)  # 15 sec total
def fetch_user_destination_address(reply_to_message_id: str) -> str:
    res = requests.get(
        url=os.environ["fetch_user_dest_addr_url"] + f"?reply_to_message_id={reply_to_message_id}",
        timeout=10,
    )
    if res.status_code != 200:
        print(res.content)
        raise Exception(f"fetch_user_destination_address failed with status {res.status_code}")

    else:
        return res.json()["email_id"]


def extract_message_id(in_reply_to_string: str) -> str:
    """
    Extracts the Message ID value from its header format.

    For Ex: If Message ID is <<EMAIL>>
    it returns "010901942b53e456-f05a4908-6050-4cdd-8e22-4843ca832231-000000"

    :param in_reply_to_string: Value of "in-reply-to" key.
    :returns: Message ID if found. Otherwise empty string.
    """
    match = re.search(r'<([^@]+)@', in_reply_to_string)
    if match:
        return match.group(1)
    else:
        return ""


@retry(requests.exceptions.Timeout, tries=3, delay=5)
def mark_bad_email(email_id: str, reason: str, bounce=False):
    res = requests.post(
        url=os.environ["mark_bad_email_url"],
        json={
            "bad_email_id": email_id,
            "reason": reason,
        },
        timeout=10,
    )
    if res.status_code != 200:
        print(res.content)
        raise Exception(f"mark_bad_email failed with status {res.status_code}")


def get_text_content(message: Message) -> str | None:
    """
    Extract text content from an email message.
    Returns plain text if available, otherwise HTML.
    Returns None only if neither is available.
    """
    # Check for plain text first in non-multipart messages
    if not message.is_multipart():
        content_type = message.get_content_type()
        if content_type == 'text/plain' or content_type == 'text/html':
            return message.get_payload(decode=True).decode(message.get_content_charset() or 'utf-8')
        return None

    # For multipart messages, prioritize text/plain over HTML
    html_content: str | None = None

    for part in message.get_payload():
        if part.get_content_type() == 'text/plain':
            return part.get_payload(decode=True).decode(part.get_content_charset() or 'utf-8')

        # Save HTML part as fallback
        if part.get_content_type() == 'text/html':
            html_content = part.get_payload(decode=True).decode(part.get_content_charset() or 'utf-8')

        # Handle nested multipart messages
        if part.is_multipart():
            nested_content: str | None = get_text_content(part)
            if nested_content is not None:
                return nested_content

    # Return HTML content if plain text wasn't found
    if html_content:
        return html_content

    return None


def get_email_body(email_s3_key):
    """
    Fetches and decodes the email content stored in an S3 bucket using the provided S3 object key.
    Parses the retrieved email content to extract plain text or HTML content. Raises an exception
    if no plain text or html content is found in the email.

    :param email_s3_key: The key of the email object stored in the S3 bucket.

    :raises Exception: If no plain text or html content is found in the email.

    :returns: Body content (plain text or html) if found, otherwise None.
    """
    response = s3_client.get_object(
        Bucket=os.environ["bucket_name"],
        Key=email_s3_key
    )

    streaming_body = response["Body"]
    content = streaming_body.read()
    text_content = content.decode('utf-8')

    # Use email module to parse `text_content` and fetch the plain text or HTML email message.
    email_parser = email.parser.Parser()
    root_message: Message = email_parser.parsestr(text_content)
    body_text: str | None = get_text_content(root_message)

    # if body_text is None:
    #     print(root_message.as_string())
    #     raise Exception("Reply does not seem to contain any plain text or html content.")

    return body_text


@retry(Exception, tries=3, delay=3)
def get_campaign_account_email(campaign_email: str) -> str:
    """
    Fetches campaign email owner's account email address.
    """
    try:
        res = requests.get(
            url=os.environ["fetch_campaign_account_email_url"] + f"?campaign_email={campaign_email}",
            timeout=10,
        )
        if res.status_code == 200:
            return res.json()["email_id"]

        else:
            print(res.content)
            raise Exception(f"get_campaign_account_email() failed with status {res.status_code}")

    except Exception as err:
        raise Exception(f"get_campaign_account_email() API call failed: {err}")


@retry(Exception, tries=3, delay=3)
def get_warmup_emails() -> List[str]:
    """
    Fetches current list of warmup email addresses from our warmup system api.
    """
    try:
        res = requests.get(
            url="https://warmup.deliveryman.ai/get-all-warmup-emails/",
            headers={
                "Content-Type": "application/json",
                "X-API-Key": os.environ["CE_EMAIL_WARMUP_API_KEY"],
            },
            timeout=10,
        )
        if res.status_code == 200:
            emails: List[str] = res.json()["emails"]
            return emails

        else:
            print(res.content)
            raise Exception(f"get_warmup_emails() failed with status {res.status_code}")

    except Exception as err:
        raise Exception(f"get_warmup_emails() API call failed: {err}")
