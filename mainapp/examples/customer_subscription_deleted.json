{"api_version": "2025-04-30.basil", "created": **********, "data": {"object": {"application": null, "application_fee_percent": null, "automatic_tax": {"disabled_reason": null, "enabled": false, "liability": null}, "billing_cycle_anchor": **********, "billing_cycle_anchor_config": null, "billing_thresholds": null, "cancel_at": null, "cancel_at_period_end": false, "canceled_at": **********, "cancellation_details": {"comment": null, "feedback": null, "reason": "cancellation_requested"}, "collection_method": "charge_automatically", "created": **********, "currency": "usd", "customer": "cus_SOtwxf8PZ7RaiT", "days_until_due": null, "default_payment_method": "pm_1RU68YSBdPcIAJFyA4Icde81", "default_source": null, "default_tax_rates": [], "description": null, "discounts": [], "ended_at": **********, "id": "sub_1RU68fSBdPcIAJFy8w1sAzVQ", "invoice_settings": {"account_tax_ids": null, "issuer": {"type": "self"}}, "items": {"data": [{"billing_thresholds": null, "created": **********, "current_period_end": **********, "current_period_start": **********, "discounts": [], "id": "si_SOtwaLqp02raox", "metadata": {}, "object": "subscription_item", "plan": {"active": true, "amount": 7900, "amount_decimal": "7900", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "id": "price_1RSFLASBdPcIAJFyksv124yk", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "meter": null, "nickname": null, "object": "plan", "product": "prod_SMzJHp4O79RToM", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "id": "price_1RSFLASBdPcIAJFyksv124yk", "livemode": false, "lookup_key": null, "metadata": {}, "nickname": null, "object": "price", "product": "prod_SMzJHp4O79RToM", "recurring": {"interval": "month", "interval_count": 1, "meter": null, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 7900, "unit_amount_decimal": "7900"}, "quantity": 1, "subscription": "sub_1RU68fSBdPcIAJFy8w1sAzVQ", "tax_rates": []}], "has_more": false, "object": "list", "total_count": 1, "url": "/v1/subscription_items?subscription=sub_1RU68fSBdPcIAJFy8w1sAzVQ"}, "latest_invoice": "in_1RU68fSBdPcIAJFyrWcaJI19", "livemode": false, "metadata": {"workspace_id": "13"}, "next_pending_invoice_item_invoice": null, "object": "subscription", "on_behalf_of": null, "pause_collection": null, "payment_settings": {"payment_method_options": {"acss_debit": null, "bancontact": null, "card": {"network": null, "request_three_d_secure": "automatic"}, "customer_balance": null, "konbini": null, "sepa_debit": null, "us_bank_account": null}, "payment_method_types": null, "save_default_payment_method": "off"}, "pending_invoice_item_interval": null, "pending_setup_intent": null, "pending_update": null, "plan": {"active": true, "amount": 7900, "amount_decimal": "7900", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "id": "price_1RSFLASBdPcIAJFyksv124yk", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "meter": null, "nickname": null, "object": "plan", "product": "prod_SMzJHp4O79RToM", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "quantity": 1, "schedule": null, "start_date": **********, "status": "canceled", "test_clock": null, "transfer_data": null, "trial_end": null, "trial_settings": {"end_behavior": {"missing_payment_method": "create_invoice"}}, "trial_start": null}}, "id": "evt_1RU6FQSBdPcIAJFyaxpcKdkb", "livemode": false, "object": "event", "pending_webhooks": 1, "request": {"id": "req_C4utw01r8Nookg", "idempotency_key": "batchapi-batch_1RU6FCSBdPcIAJFynAS7EdaD-cus_SOtwxf8PZ7RaiT_delete_customer"}, "type": "customer.subscription.deleted"}