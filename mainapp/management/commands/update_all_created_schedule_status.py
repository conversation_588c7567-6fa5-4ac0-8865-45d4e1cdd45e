from django.core.management import BaseCommand

from mainapp.models import CampaignSchedule, Campaign


class Command(BaseCommand):
    """
    One time use only on prod - 15th July 2025 update. Delete this later.

    We need to do this for running campaigns since all of their scheduels have already been created on AWS.
    """

    def handle(self, *args, **options):
        # Fetch all running campaigns.
        for camp in Campaign.objects.filter(status="running"):
            # For each campaign, bulk update all schedules that are in "created" status to "scheduled" status.
            bulk_updates = []
            for schedule in camp.campaignschedule_set.filter(status="created"):
                schedule.status = "scheduled"
                bulk_updates.append(schedule)

            CampaignSchedule.objects.bulk_update(bulk_updates, ["status"], batch_size=5000)

            self.stdout.write(
                self.style.SUCCESS(f"[*] {len(bulk_updates)} schedules in campaign \"{camp.name}\" have been updated.")
            )
