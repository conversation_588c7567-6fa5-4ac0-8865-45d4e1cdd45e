# Generated by Django 5.1.2 on 2025-06-24 09:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0069_campaign_test_email_destinations'),
    ]

    operations = [
        migrations.AddField(
            model_name='campaign',
            name='add_unsub_link',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='campaign',
            name='custom_unsub_text',
            field=models.TextField(default='Want to opt out? Just reply with'),
        ),
        migrations.AddField(
            model_name='campaign',
            name='is_html_email',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='campaign',
            name='unsub_link_type',
            field=models.CharField(default='auto', max_length=100),
        ),
    ]
