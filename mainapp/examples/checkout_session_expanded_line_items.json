{"adaptive_pricing": null, "after_expiration": null, "allow_promotion_codes": null, "amount_subtotal": 239900, "amount_total": 283082, "automatic_tax": {"enabled": false, "liability": null, "provider": null, "status": null}, "billing_address_collection": "required", "cancel_url": "http://localhost:3000/manage-subscription", "client_reference_id": null, "client_secret": null, "collected_information": {"shipping_details": null}, "consent": null, "consent_collection": null, "created": **********, "currency": "inr", "currency_conversion": null, "custom_fields": [], "custom_text": {"after_submit": null, "shipping_address": null, "submit": null, "terms_of_service_acceptance": null}, "customer": "cus_SR5T6c4ObZdV4H", "customer_creation": null, "customer_details": {"address": {"city": "Navi Mumbai", "country": "IN", "line1": "101, Elite C.H.S, Plot No. 85", "line2": "Sector 10A, Vashi", "postal_code": "400703", "state": "MH"}, "email": "<EMAIL>", "name": "<PERSON><PERSON>", "phone": null, "tax_exempt": "none", "tax_ids": []}, "customer_email": null, "discounts": [], "expires_at": **********, "id": "cs_test_a1b0H4ZovrqNZb0i3QjFf2yA4ZwOfw9IayzABuxSt8JeRzi9KfkiqwxcFu", "invoice": "in_1RWDJjSBdPcIAJFy6KGAwt0x", "invoice_creation": null, "line_items": {"data": [{"amount_discount": 0, "amount_subtotal": 239900, "amount_tax": 43182, "amount_total": 283082, "currency": "inr", "description": "Basic", "id": "li_1RWDIkSBdPcIAJFyFkK4DAaW", "object": "item", "price": {"active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "id": "price_1RUmb5SBdPcIAJFyeaq2y2ij", "livemode": false, "lookup_key": "basic_monthly", "metadata": {}, "nickname": null, "object": "price", "product": "prod_SMzJK4cilGyIy0", "recurring": {"interval": "month", "interval_count": 1, "meter": null, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 2900, "unit_amount_decimal": "2900"}, "quantity": 1}], "has_more": false, "object": "list", "url": "/v1/checkout/sessions/cs_test_a1b0H4ZovrqNZb0i3QjFf2yA4ZwOfw9IayzABuxSt8JeRzi9KfkiqwxcFu/line_items"}, "livemode": false, "locale": null, "metadata": {}, "mode": "subscription", "object": "checkout.session", "payment_intent": null, "payment_link": null, "payment_method_collection": "always", "payment_method_configuration_details": {"id": "pmc_1RSETZSBdPcIAJFycJbp65Lx", "parent": null}, "payment_method_options": {"card": {"request_three_d_secure": "automatic"}}, "payment_method_types": ["card"], "payment_status": "paid", "permissions": null, "phone_number_collection": {"enabled": false}, "recovered_from": null, "saved_payment_method_options": {"allow_redisplay_filters": ["always"], "payment_method_remove": "disabled", "payment_method_save": null}, "setup_intent": null, "shipping_address_collection": null, "shipping_cost": null, "shipping_options": [], "status": "complete", "submit_type": null, "subscription": "sub_1RWDJiSBdPcIAJFybkufwwHg", "success_url": "https://aicaller-frontend.abun.com/checkout/success?session_id={CHECKOUT_SESSION_ID}", "total_details": {"amount_discount": 0, "amount_shipping": 0, "amount_tax": 43182}, "ui_mode": "hosted", "url": null, "wallet_options": null}