# Generated by Django 5.1.2 on 2025-03-20 12:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0028_campaign_sending_domains'),
    ]

    operations = [
        migrations.AddField(
            model_name='managedsubdomain',
            name='warmup_email_count',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.CreateModel(
            name='WarmupSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('schedule_datetime', models.DateTimeField()),
                ('email', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.emailid')),
            ],
        ),
    ]
