# Generated by Django 5.1.2 on 2025-01-04 11:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0011_bademail_alter_campaignschedule_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='CampaignIgnoredContacts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_id', models.CharField(max_length=500)),
                ('reason', models.TextField()),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaign')),
            ],
        ),
    ]
