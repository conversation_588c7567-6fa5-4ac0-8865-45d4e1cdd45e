import os
from datetime import datetime

from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.email_messages import domain_warmed_up_email_message
from mainapp.models import ManagedSubdomain
from mainapp.tasks import send_email_task


class Command(BaseCommand):
    def handle(self, *args, **options):
        # Get current datetime object in UTC.
        current_time = timezone.now()

        # Fetch all connected domains that are in warmup status.
        for msub in ManagedSubdomain.objects.filter(status="warmup"):
            if current_time > msub.domain_usable_from:
                msub.status = "active"
                msub.save()
                # Send email to user.
                if not msub.workspace.user.has_sent_domain_warmed_up_email:
                    send_email_task.delay(
                        to=msub.workspace.user.email,
                        sender="<EMAIL>",
                        sender_name="Team DeliverymanAI",
                        subject=f"{msub.subdomain} is ready to start sending email campaigns",
                        body_html=domain_warmed_up_email_message(
                            username=msub.workspace.user.username,
                            create_campaign_url=os.environ["CE_APP_HOST_URL"] + "/campaigns/create",
                            current_year=str(datetime.now().year),
                        )
                    )
                    msub.workspace.user.has_sent_domain_warmed_up_email = True
                    self.stdout.write(self.style.SUCCESS(f"[*] Email sent to {msub.workspace.user.email}"))

                if msub.workspace.user.first_domain_connected_on is None:
                    msub.workspace.user.first_domain_connected_on = timezone.now()
                    self.stdout.write(self.style.SUCCESS(
                        f"[*] First domain connection time set for {msub.workspace.user.email}"
                    ))

                msub.workspace.user.save()

                self.stdout.write(self.style.SUCCESS(f"[*] {msub.subdomain} has been marked as active."))

        self.stdout.write("All Done!")
