from django.core.management.base import BaseCommand
from mainapp.models import ManagedSubdomain
from mainapp.tasks import check_blacklist_batch_task

"""
This should be run as a cronjob everyday.
"""

class Command(BaseCommand):
    help = "Queue blacklist checks for all connected domains in safe batches"

    def handle(self, *args, **options):
        all_subs = list(
            ManagedSubdomain.objects
            .filter(setup_complete=True)
            .values_list('id', flat=True)
        )

        batch_size = 100
        total = len(all_subs)
        queued = 0

        for i in range(0, total, batch_size):
            batch_ids = all_subs[i:i + batch_size]
            check_blacklist_batch_task.delay(batch_ids)
            queued += len(batch_ids)

        batch_count = (total // batch_size) + (1 if total % batch_size else 0)
        self.stdout.write(self.style.SUCCESS(f"[*] Queued {queued} subdomains in {batch_count} batches."))