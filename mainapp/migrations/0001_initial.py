# Generated by Django 5.1.2 on 2024-11-26 09:43

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_admin', models.BooleanField(default=False)),
                ('email', models.EmailField(db_index=True, max_length=254, unique=True)),
                ('username', models.CharField(max_length=150)),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EmailID',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_address', models.CharField(max_length=1000, unique=True)),
                ('email_active', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='EmailSubdomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('subdomain', models.CharField(max_length=500, unique=True)),
                ('dkim_tokens', models.JSONField(default=list)),
                ('forwardemail_verification_record', models.TextField(default=None, null=True)),
            ],
            options={
                'ordering': ['created_on'],
            },
        ),
        migrations.CreateModel(
            name='UnsubscribedEmail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_id', models.CharField(db_index=True, max_length=500, unique=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Campaign',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(max_length=255, unique=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=300)),
                ('status', models.CharField(choices=[('created', 'Created'), ('running', 'Running'), ('paused', 'Paused'), ('complete', 'Complete')], default='created', max_length=100)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CampaignContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(db_index=True, max_length=255, unique=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('email_id', models.CharField(max_length=500)),
                ('attributes', models.JSONField(default=dict)),
                ('email_schedule_names', models.JSONField(default=list)),
                ('contact_closed', models.BooleanField(default=False)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaign')),
                ('sending_email', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.emailid')),
            ],
        ),
        migrations.CreateModel(
            name='CampaignEmailMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(max_length=255, unique=True)),
                ('order', models.IntegerField(default=0)),
                ('subject', models.TextField()),
                ('body', models.TextField()),
                ('next_message_days', models.PositiveSmallIntegerField(default=1)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaign')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='CampaignSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(db_index=True, max_length=255, unique=True)),
                ('schedule_name', models.CharField(max_length=500)),
                ('schedule_datetime', models.DateTimeField()),
                ('status', models.CharField(choices=[('created', 'Created'), ('sent', 'Sent'), ('failed', 'Failed'), ('replied', 'Replied'), ('cancelled_reply_received', 'Cancelled - Reply Received'), ('cancelled_unsubscribed', 'Cancelled - Unsubscribed')], default='created', max_length=100)),
                ('sent_on', models.DateTimeField(default=None, null=True)),
                ('message_id', models.CharField(db_index=True, default=None, max_length=100, null=True)),
                ('reply_received', models.BooleanField(default=False)),
                ('campaign', models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaign')),
                ('contact', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaigncontact')),
                ('email_message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaignemailmessage')),
            ],
        ),
        migrations.AddField(
            model_name='emailid',
            name='email_subdomain',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.emailsubdomain'),
        ),
        migrations.CreateModel(
            name='ManagedSubdomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subdomain', models.CharField(max_length=500, unique=True)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('setup_complete', models.BooleanField(default=False)),
                ('current_setup_stage', models.IntegerField(default=0)),
                ('naming_strategy', models.CharField(choices=[('random', 'random'), ('male', 'male'), ('female', 'female'), ('custom', 'custom')], default='random', max_length=300)),
                ('custom_name', models.CharField(default=None, max_length=500, null=True)),
                ('contacts_count', models.PositiveIntegerField(default=1)),
                ('hosted_zone_id', models.CharField(default=None, max_length=500, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='emailsubdomain',
            name='managed_subdomain',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.managedsubdomain'),
        ),
    ]
