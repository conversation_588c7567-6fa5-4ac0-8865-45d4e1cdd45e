from django.core.management import BaseCommand

from mainapp.models import User, Workspace

"""
Management command to find all campaigns with non-null scheduled datetime and start them if it is time.
To be run as a CronJob every 5 mins.
"""


class Command(BaseCommand):
    def handle(self, *args, **options):
        for user in User.objects.all():
            if user.active_workspace is None:
                # create default workspace.
                default_workspace = Workspace.objects.create(user=user)
                user.active_workspace = default_workspace
                user.save()
                self.stdout.write(f"[*] Successfully created default workspace for {user.email}")

            else:
                default_workspace = user.active_workspace
                self.stdout.write(f"[-] {user.email} already has a default workspace.")

            # move managed subdomains to workspace.
            for msub in user.managedsubdomain_set.all():
                msub.workspace = default_workspace
                msub.save()
            self.stdout.write(f"[*] Moved all managed subdomains for {user.email} to {default_workspace.name}.")

            # move campaigns to workspace.
            for campaign in user.campaign_set.all():
                campaign.workspace = default_workspace
                campaign.save()
            self.stdout.write(f"[*] Moved all campaigns for {user.email} to {default_workspace.name}.")

            # move unsubscribed emails to workspace.
            for unsub_emails in user.unsubscribedemail_set.all():
                unsub_emails.workspace = default_workspace
                unsub_emails.save()
            self.stdout.write(f"[*] Moved all unsubscribed emails for {user.email} to {default_workspace.name}.")

        self.stdout.write("[*] All done!")
