import logging
from typing import Dict, List

from django.http import HttpResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny

from mainapp.campaign import CampaignManager
from mainapp.models import CampaignSchedule, CampaignContact
from mainapp.utils import extract_message_id

if logging.DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


"""
===================================================================================================
-------------------------- DEPRECATED VIEWS FILE. PLEASE DO NOT USE THIS --------------------------
===================================================================================================
"""


@api_view(["POST"])
@permission_classes([AllowAny])
def webhook_forwardemail(request):
    webhook_data = request.data
    to_address: str = webhook_data["to"]["value"][0]["address"]

    # -------------------- UNSUBSCRIBE Handler --------------------
    if to_address.split("@")[0] == "unsubscribe":
        # Fetch the contact using uid from email subject and unsubscribe them.
        subject: str = webhook_data["subject"].replace("Re: ", "")
        contact_uid: str = subject.split(":")[-1]
        contact = CampaignContact.objects.get(uid=contact_uid)
        CampaignManager.unsubscribe_contact(contact)
        return HttpResponse()

    # -------------------- Other Replies --------------------
    header_data: List[Dict] = webhook_data["headerLines"]  # Has 2 keys - "key" & "line"

    reply_message_id: str = ""
    for data in header_data:
        if data["key"] == "in-reply-to":
            reply_message_id = extract_message_id(data["line"])

    if not reply_message_id:
        # We'll ignore this
        logger.warning(f"webhook_forwardemail() - Reply message was ignored since message id could not be found.")
        return HttpResponse()

    try:
        campaign_contact_email = CampaignSchedule.objects.get(message_id=reply_message_id)
    except CampaignSchedule.DoesNotExist:
        logger.error(f"webhook_forwardemail() - Reply message was ignored since CampaignContactEmail with "
                     f"message id {reply_message_id} could not be found.")
        return HttpResponse()

    campaign_contact_email.reply_received = True
    campaign_contact_email.status = "replied"
    campaign_contact_email.save()

    # Mark and delete all remaining schedules for this user.
    for cce in CampaignSchedule.objects.filter(
            contact=campaign_contact_email.contact,
            status__in=["created", "sent"]
    ):
        CampaignManager.end_campaign_for_contact(cce.contact)

    return HttpResponse()
