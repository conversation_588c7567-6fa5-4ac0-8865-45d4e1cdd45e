# Generated by Django 5.1.2 on 2025-09-03 10:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0092_campaignemailmessage_email_content_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='HubspotIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('access_token', models.CharField(max_length=255)),
                ('refresh_token', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('workspace', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='mainapp.workspace')),
            ],
        ),
    ]
