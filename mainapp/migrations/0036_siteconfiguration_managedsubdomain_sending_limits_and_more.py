# Generated by Django 5.1.2 on 2025-04-24 13:06

import mainapp.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0035_alter_campaignemailmessage_score_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain_sending_limits', models.JSONField(default=mainapp.models.get_default_sending_limits)),
            ],
        ),
        migrations.AddField(
            model_name='managedsubdomain',
            name='sending_limits',
            field=models.JSONField(default=mainapp.models.get_default_sending_limits),
        ),
        migrations.AlterField(
            model_name='campaign',
            name='status',
            field=models.CharField(choices=[('creating', 'Creating'), ('created', 'Created'), ('scheduled', 'Scheduled'), ('running', 'Running'), ('paused', 'Paused'), ('complete', 'Complete'), ('cancelled', 'Cancelled')], default='creating', max_length=100),
        ),
    ]
