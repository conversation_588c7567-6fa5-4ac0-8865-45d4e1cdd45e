from django.http import JsonResponse


class JsonResponseSuccess(JsonResponse):
    def __init__(self, data: dict = None):
        default_data = {"status_code": 200, "status_text": "OK"}
        data = {**default_data, **data} if data else default_data
        super(JsonResponseSuccess, self).__init__(status=200, data=data)


class JsonResponseBadRequest(JsonResponse):
    def __init__(self, data: dict = None):
        default_data = {"status_code": 400, "status_text": "Bad Request"}
        data = {**default_data, **data} if data else default_data
        super(JsonResponseBadRequest, self).__init__(status=400, data=data)


class JsonResponseNotFound(JsonResponse):
    def __init__(self, data: dict = None):
        default_data = {"status_code": 404, "status_text": "Not Found"}
        data = {**default_data, **data} if data else default_data
        super(JsonResponseNotFound, self).__init__(status=404, data=data)


class JsonResponseServerError(JsonResponse):
    def __init__(self, data: dict = None):
        default_data = {"status_code": 500, "status_text": "Server Error"}
        data = {**default_data, **data} if data else default_data
        super(JsonResponseServerError, self).__init__(status=500, data=data)


class JsonResponseUnauthorized(JsonResponse):
    def __init__(self, data: dict = None):
        default_data = {"status_code": 401, "status_text": "Unauthorized"}
        data = {**default_data, **data} if data else default_data
        super(JsonResponseUnauthorized, self).__init__(status=401, data=data)


class JsonResponseForbidden(JsonResponse):
    def __init__(self, data: dict = None):
        default_data = {"status_code": 403, "status_text": "Forbidden"}
        data = {**default_data, **data} if data else default_data
        super(JsonResponseForbidden, self).__init__(status=403, data=data)


class JsonResponseRedirect(JsonResponse):
    def __init__(self, redirect_url: str):
        super(JsonResponseRedirect, self).__init__(
            status=301,
            data={"status_code": 301, "status_text": "Forbidden", "redirect_url": redirect_url}
        )
