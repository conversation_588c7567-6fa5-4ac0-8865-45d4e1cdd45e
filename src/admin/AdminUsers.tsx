import {Box, Divider, Stack, Typography, useTheme} from "@mui/material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {CustomCellRendererProps} from "ag-grid-react";
import {Link} from "react-router-dom";
import {urls} from "@routes";
import { format } from "date-fns";


interface User {
	id: number
	username: string
	email: string
	total_domains: number
	total_campaigns: number
	date_joined_ts: number
	plan_name: string
}

interface PageData {
	status_code: number
	status_text: string

	users: User[]
}


export default function AdminUsers() {
	const theme = useTheme();

	const [pageData, setPageData] = useState<PageData>();

	// Query to fetch all users.
	const fetchAllUsersQuery = useQuery({
		queryKey: ["fetchAllUsersQuery"],
		queryFn: () => authenticateAndFetchData("/admin/all-users/"),
		gcTime: 0,
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (fetchAllUsersQuery.data) {
			setPageData(fetchAllUsersQuery.data.data as PageData);
		}
	}, [fetchAllUsersQuery.data]);

	const UserDetailsLink = (cellProps: CustomCellRendererProps) => {
		return (
			<Link to={urls["adminUserDetails"].replace(":userId", cellProps.data["id"])}
						style={{color: theme.palette.text.primary}}>
				{cellProps.value}
			</Link>
		)
	}

	function timestampFormatter(params: ValueFormatterParams) {
		if (params.value === null) {
			return "---"
		}
		return format(new Date(params.value), "do MMM y, h:mm a");
	}

	// Columns for user table.
	const columnDefs: ColDef[] = [
		{field: "email", headerName: "Email ID", cellRenderer: UserDetailsLink},
		{field: "username", headerName: "Username"},
		{field: "id", headerName: "DB ID"},
		{field: "date_joined_ts", headerName: "Created On", valueFormatter: timestampFormatter},
		{field: "total_domains", headerName: "Total Domains"},
		{field: "total_campaigns", headerName: "Total Campaigns", resizable: false},
		{ field: "last_activity", headerName: "Last Activity", resizable: false, valueFormatter: timestampFormatter },
		{field: "plan_name", headerName: "Plan Name"},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (fetchAllUsersQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (fetchAllUsersQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={fetchAllUsersQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Typography fontWeight={"bold"} variant={"h5"}>Manage All User Accounts</Typography>
				<Divider/>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs} rows={pageData?.users}/>
				</Box>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
