{"id": "evt_1RVY23SBdPcIAJFySa38LGa0", "object": "event", "api_version": "2025-04-30.basil", "created": 1748869442, "data": {"object": {"id": "sub_1RVXRrSBdPcIAJFyfUYzWI9o", "object": "subscription", "application": null, "application_fee_percent": null, "automatic_tax": {"disabled_reason": null, "enabled": false, "liability": null}, "billing_cycle_anchor": **********, "billing_cycle_anchor_config": null, "billing_thresholds": null, "cancel_at": null, "cancel_at_period_end": false, "canceled_at": null, "cancellation_details": {"comment": null, "feedback": null, "reason": null}, "collection_method": "charge_automatically", "created": **********, "currency": "inr", "customer": "cus_SQOEW5aL7Y3OJc", "days_until_due": null, "default_payment_method": "pm_1RVXRlSBdPcIAJFy2KRuZqxa", "default_source": null, "default_tax_rates": [], "description": null, "discounts": [], "ended_at": null, "invoice_settings": {"account_tax_ids": null, "issuer": {"type": "self"}}, "items": {"object": "list", "data": [{"id": "si_SQOEvIXitrHmmi", "object": "subscription_item", "billing_thresholds": null, "created": **********, "current_period_end": **********, "current_period_start": **********, "discounts": [], "metadata": {}, "plan": {"id": "price_1RUmb5SBdPcIAJFyeaq2y2ij", "object": "plan", "active": true, "amount": 2900, "amount_decimal": "2900", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "meter": null, "nickname": null, "product": "prod_SMzJK4cilGyIy0", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"id": "price_1RUmb5SBdPcIAJFyeaq2y2ij", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": "basic_monthly", "metadata": {}, "nickname": null, "product": "prod_SMzJK4cilGyIy0", "recurring": {"interval": "month", "interval_count": 1, "meter": null, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 2900, "unit_amount_decimal": "2900"}, "quantity": 1, "subscription": "sub_1RVXRrSBdPcIAJFyfUYzWI9o", "tax_rates": [{"id": "txr_1RUmpESBdPcIAJFyMbyBNUg3", "object": "tax_rate", "active": true, "country": "IN", "created": **********, "description": null, "display_name": "GST", "effective_percentage": 18, "flat_amount": null, "inclusive": false, "jurisdiction": null, "jurisdiction_level": null, "livemode": false, "metadata": {}, "percentage": 18, "rate_type": "percentage", "state": null, "tax_type": null}]}], "has_more": false, "total_count": 1, "url": "/v1/subscription_items?subscription=sub_1RVXRrSBdPcIAJFyfUYzWI9o"}, "latest_invoice": "in_1RVY21SBdPcIAJFyKAiMYgIS", "livemode": false, "metadata": {"workspace_id": "13"}, "next_pending_invoice_item_invoice": null, "on_behalf_of": null, "pause_collection": null, "payment_settings": {"payment_method_options": {"acss_debit": null, "bancontact": null, "card": {"network": null, "request_three_d_secure": "automatic"}, "customer_balance": null, "konbini": null, "sepa_debit": null, "us_bank_account": null}, "payment_method_types": null, "save_default_payment_method": "off"}, "pending_invoice_item_interval": null, "pending_setup_intent": null, "pending_update": {"billing_cycle_anchor": **********, "expires_at": **********, "subscription_items": [{"id": "si_SQOEvIXitrHmmi", "object": "subscription_item", "billing_thresholds": null, "created": **********, "current_period_end": **********, "current_period_start": **********, "discounts": [], "metadata": {}, "plan": {"id": "price_1RUmdkSBdPcIAJFyTU45AMaK", "object": "plan", "active": true, "amount": 7900, "amount_decimal": "7900", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "meter": null, "nickname": null, "product": "prod_SMzJHp4O79RToM", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "price": {"id": "price_1RUmdkSBdPcIAJFyTU45AMaK", "object": "price", "active": true, "billing_scheme": "per_unit", "created": **********, "currency": "usd", "custom_unit_amount": null, "livemode": false, "lookup_key": "advance_monthly", "metadata": {}, "nickname": null, "product": "prod_SMzJHp4O79RToM", "recurring": {"interval": "month", "interval_count": 1, "meter": null, "trial_period_days": null, "usage_type": "licensed"}, "tax_behavior": "unspecified", "tiers_mode": null, "transform_quantity": null, "type": "recurring", "unit_amount": 7900, "unit_amount_decimal": "7900"}, "quantity": 1, "subscription": "sub_1RVXRrSBdPcIAJFyfUYzWI9o", "tax_rates": [{"id": "txr_1RUmpESBdPcIAJFyMbyBNUg3", "object": "tax_rate", "active": true, "country": "IN", "created": **********, "description": null, "display_name": "GST", "effective_percentage": 18, "flat_amount": null, "inclusive": false, "jurisdiction": null, "jurisdiction_level": null, "livemode": false, "metadata": {}, "percentage": 18, "rate_type": "percentage", "state": null, "tax_type": null}]}], "trial_end": null, "trial_from_plan": null}, "plan": {"id": "price_1RUmb5SBdPcIAJFyeaq2y2ij", "object": "plan", "active": true, "amount": 2900, "amount_decimal": "2900", "billing_scheme": "per_unit", "created": **********, "currency": "usd", "interval": "month", "interval_count": 1, "livemode": false, "metadata": {}, "meter": null, "nickname": null, "product": "prod_SMzJK4cilGyIy0", "tiers_mode": null, "transform_usage": null, "trial_period_days": null, "usage_type": "licensed"}, "quantity": 1, "schedule": null, "start_date": **********, "status": "active", "test_clock": null, "transfer_data": null, "trial_end": null, "trial_settings": {"end_behavior": {"missing_payment_method": "create_invoice"}}, "trial_start": null}, "previous_attributes": {"latest_invoice": "in_1RVXRrSBdPcIAJFyvG49ZG07", "pending_update": null}}, "livemode": false, "pending_webhooks": 1, "request": {"id": "req_1ekhehSbVs5DOT", "idempotency_key": "16cd2d8e-5c87-4e64-b2fe-f927a1115266"}, "type": "customer.subscription.updated"}