import logging
from functools import wraps

from mainapp.models import User
from mainapp.responses import JsonResponseForbidden

logger = logging.getLogger("aicaller.decorators")


def staff_only(view_func):
    """
    IMPORTANT: Always use this after @permission_classes decorator or `request.user` won't be correct.
    """
    @wraps(view_func)
    def check_if_staff(request, *args, **kwargs):
        user: User = request.user
        if user.is_admin:
            return view_func(request, *args, **kwargs)
        else:
            return JsonResponseForbidden(data={"message": "Access Forbidden."})

    return check_if_staff
