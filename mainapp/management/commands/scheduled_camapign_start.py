from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.models import Campaign
from mainapp.tasks import start_campaign_task

"""
Management command to find all campaigns with non-null scheduled datetime and start them if it is time.
To be run as a CronJob every 5 mins.
"""


class Command(BaseCommand):
    def handle(self, *args, **options):
        self.stdout.write("[*] Checking for any campaign with active schedules...")
        for campaign in Campaign.objects.filter(scheduled_start_datetime__isnull=False):
            current_time = timezone.now()
            if current_time > campaign.scheduled_start_datetime:
                self.stdout.write(f"[*] Starting campaign {campaign.uid}...")

                # Start celery task.
                start_campaign_task.delay(campaign.uid)

                # Update campaign status.
                campaign.is_generating_schedules = True
                campaign.scheduled_start_datetime = None
                campaign.status = "running"
                campaign.save()

        self.stdout.write("[*] All done!")
