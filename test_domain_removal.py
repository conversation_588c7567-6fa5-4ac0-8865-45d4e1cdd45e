#!/usr/bin/env python
"""
Test script for domain removal functionality.
This script tests the domain removal and email rescheduling logic.
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append('/Users/<USER>/Developer/deliveryman/coldemailerbackend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ColdEmailerBackend.settings')
django.setup()

from mainapp.models import Campaign, ManagedSubdomain, CampaignContact, EmailID
from mainapp.campaign import CampaignManager


def test_domain_removal_logic():
    """
    Test the domain removal and email rescheduling logic.
    This is a dry-run test that doesn't modify the database.
    """
    print("Testing domain removal logic...")
    
    # Test the core logic without database operations
    print("✓ Testing round-robin email assignment logic")
    
    # Simulate contacts and emails
    contacts = [f"contact_{i}" for i in range(10)]
    emails = [f"email_{i}@domain.com" for i in range(3)]
    
    # Test round-robin assignment
    assignments = {}
    email_index = 0
    
    for contact in contacts:
        assignments[contact] = emails[email_index]
        email_index = (email_index + 1) % len(emails)
    
    print(f"✓ Assigned {len(contacts)} contacts to {len(emails)} emails")
    
    # Verify distribution
    email_counts = {}
    for email in emails:
        email_counts[email] = sum(1 for assigned_email in assignments.values() if assigned_email == email)
    
    print("Email distribution:")
    for email, count in email_counts.items():
        print(f"  {email}: {count} contacts")
    
    # Check if distribution is balanced
    min_count = min(email_counts.values())
    max_count = max(email_counts.values())
    
    if max_count - min_count <= 1:
        print("✓ Email distribution is balanced")
    else:
        print("✗ Email distribution is not balanced")
        return False
    
    print("✓ Domain removal logic test passed")
    return True


def test_api_endpoints():
    """
    Test that our new API endpoints are properly defined.
    """
    print("\nTesting API endpoint definitions...")
    
    try:
        from mainapp.views.views_main import get_campaign_domains, remove_campaign_domain
        print("✓ API functions imported successfully")
        
        # Check function signatures
        import inspect
        
        get_domains_sig = inspect.signature(get_campaign_domains)
        remove_domain_sig = inspect.signature(remove_campaign_domain)
        
        print(f"✓ get_campaign_domains signature: {get_domains_sig}")
        print(f"✓ remove_campaign_domain signature: {remove_domain_sig}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import API functions: {e}")
        return False


def test_url_patterns():
    """
    Test that URL patterns are properly configured.
    """
    print("\nTesting URL patterns...")
    
    try:
        from django.urls import reverse
        
        # Test URL pattern resolution
        domains_url = reverse('get_campaign_domains')
        remove_url = reverse('remove_campaign_domain')
        
        print(f"✓ get_campaign_domains URL: {domains_url}")
        print(f"✓ remove_campaign_domain URL: {remove_url}")
        
        return True
        
    except Exception as e:
        print(f"✗ URL pattern test failed: {e}")
        return False


def test_campaign_manager_method():
    """
    Test that the new CampaignManager method exists.
    """
    print("\nTesting CampaignManager method...")
    
    try:
        # Check if the method exists
        if hasattr(CampaignManager, 'remove_domain_and_reschedule'):
            print("✓ remove_domain_and_reschedule method exists")
            
            # Check method signature
            import inspect
            method_sig = inspect.signature(CampaignManager.remove_domain_and_reschedule)
            print(f"✓ Method signature: {method_sig}")
            
            return True
        else:
            print("✗ remove_domain_and_reschedule method not found")
            return False
            
    except Exception as e:
        print(f"✗ CampaignManager method test failed: {e}")
        return False


def main():
    """
    Run all tests.
    """
    print("=" * 60)
    print("DOMAIN REMOVAL FUNCTIONALITY TEST")
    print("=" * 60)
    
    tests = [
        test_domain_removal_logic,
        test_api_endpoints,
        test_url_patterns,
        test_campaign_manager_method,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The domain removal functionality is ready.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
