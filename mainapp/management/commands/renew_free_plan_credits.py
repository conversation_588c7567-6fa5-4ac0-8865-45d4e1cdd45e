from dateutil.relativedelta import relativedelta
from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.models import Workspace


class Command(BaseCommand):
    def handle(self, *args, **options):
        current_time = timezone.now()

        # Iterate through all free plan workspaces whose next renewal date has crossed
        for workspace in Workspace.objects.filter(
                subscription_plan__is_free_plan=True,
                next_renewal_date__lte=current_time
        ):
            workspace.credits_remaining = workspace.subscription_plan.monthly_email_sending_quota
            workspace.next_renewal_date = current_time + relativedelta(months=+1)
            workspace.save()

            self.stdout.write(f"[*] Credits added for workspace {workspace.name} (id: {workspace.id})")

        self.stdout.write("[*] All Done!")
