import uuid
from typing import List

from ColdEmailerBackend.settings import aws_route53


def create_hosted_zone(fqdn: str, user_email: str) -> str:
    """
    Creates route53 hosted zone for given FQDN.
    :param fqdn: ex. hey.example.com, example.com
    :param user_email: account user email id.
    :returns: The ID that Amazon Route 53 assigned to the hosted zone when you created it. Save this for future use.
    """
    # Create the zone.
    res = aws_route53.create_hosted_zone(
        Name=fqdn,
        CallerReference=uuid.uuid4().hex,
        HostedZoneConfig={
            "Comment": f"Managed subdomain for {user_email}",
            "PrivateZone": False
        }
    )
    return res["HostedZone"]["Id"]


def get_hosted_zone_nameservers(hosted_zone_id: str) -> List[str]:
    """
    Returns list of nameservers for this hosted zone.

    :param hosted_zone_id: The ID of the hosted zone that you want to get information about.
    :return: List of nameservers.
    """
    # Get the nameservers
    res = aws_route53.get_hosted_zone(Id=hosted_zone_id)
    nameservers: List[str] = res["DelegationSet"]["NameServers"]

    return nameservers
