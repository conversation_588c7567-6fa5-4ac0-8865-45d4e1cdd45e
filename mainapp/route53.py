import logging
from typing import Literal, List, Dict

import botocore
from botocore.exceptions import ClientError
from retry import retry

from ColdEmailerBackend.settings import aws_route53, DEBUG

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


@retry(ClientError, tries=5, delay=3, backoff=2)
def add_route53_record(hosted_zone_id: str, record_type: Literal["CNAME", "MX", "TXT"],
                       record_name: str, record_value: str | List[str], ttl=60):
    """
    Adds DNS record to a Route53 hosted zone.

    For now, record_type can only be CNAME, MX, TXT.

    NOTE: For MX records you need to provide priority in the Value itself. For example:
    10 mail1.example.com.
    20 mail2.example.com.

    For list of possible exceptions check:
    https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/route53/client/change_resource_record_sets.html

    :param hosted_zone_id: The ID of the hosted zone where you want to add the record(s).
    :param record_type: The DNS record type.
    :param record_name: Name of the record.
    :param record_value: DNS record value.
    :param ttl: The TTL of the record in seconds.
    :returns: Dictionary with "success" as True if operation was successful. Otherwise False.
    """
    if type(record_value) is str:
        resource_records = [{
            "Value": record_value,
        }]
    else:
        resource_records = [{"Value": value} for value in record_value]

    aws_route53.change_resource_record_sets(
        HostedZoneId=hosted_zone_id,
        ChangeBatch={
            "Changes": [
                {
                    "Action": "UPSERT",
                    "ResourceRecordSet": {
                        "Name": record_name,
                        "Type": record_type,
                        "ResourceRecords": resource_records,
                        "TTL": ttl,
                    }
                }
            ]
        }
    )


@retry(Exception, tries=5, delay=3, backoff=2)
def delete_route53_records(hosted_zone_id: str, records: List[Dict]):
    """
    Deletes given records from Route53 hosted zone.

    :param hosted_zone_id: The ID of the hosted zone that contains the resource record sets that you want to delete.
    :param records: List of Dictionary. Each record should contain at least 'Name' and 'Type'.
    :returns: Change ID.
    """
    logger.debug(f"[*] Deleting records for {hosted_zone_id}")
    try:
        aws_route53.change_resource_record_sets(
            HostedZoneId=hosted_zone_id,
            ChangeBatch={
                "Changes": [{"Action": "DELETE", "ResourceRecordSet": record} for record in records]
            }
        )

    except aws_route53.exceptions.InvalidChangeBatch as err:
        logger.warning(f"delete_route53_records() - {err}")


def get_route53_change_status(change_id: str) -> Literal["PENDING", "INSYNC"]:
    """
    Returns current status of given change.

    :param change_id: The ID of the change batch request
    :returns: Current status of the change.
    """
    return aws_route53.get_change(Id=change_id)["ChangeInfo"]["Status"]
