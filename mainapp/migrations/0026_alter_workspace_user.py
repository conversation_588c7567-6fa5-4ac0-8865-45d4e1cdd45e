# Generated by Django 5.1.2 on 2025-02-18 09:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0025_remove_campaign_user_remove_managedsubdomain_user_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='workspace',
            name='user',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
