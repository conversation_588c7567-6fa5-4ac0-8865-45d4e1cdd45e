# Generated by Django 5.1.2 on 2025-01-20 14:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0016_user_google_api_auth_creds_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GooglePostmasterIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domains_added', models.J<PERSON><PERSON>ield(default=list)),
            ],
        ),
        migrations.AddField(
            model_name='user',
            name='google_postmaster_integration',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='mainapp.googlepostmasterintegration'),
        ),
    ]
