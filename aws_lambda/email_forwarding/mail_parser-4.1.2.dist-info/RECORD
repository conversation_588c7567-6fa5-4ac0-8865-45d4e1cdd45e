../../bin/mail-parser,sha256=HBsv9FjoD81CLdfwp0lCHo5NAv9vC1aZaVMKcsXxpvs,270
mail_parser-4.1.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mail_parser-4.1.2.dist-info/LICENSE.txt,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
mail_parser-4.1.2.dist-info/METADATA,sha256=fjmvnSkZ5b3GD5WYZmK6jysVKFyPP5gT-TCU9ZEHRIs,10465
mail_parser-4.1.2.dist-info/NOTICE.txt,sha256=YPqTsuv_tZkJM4iqn9gcmJ1CRGNolE7QfffqAzv5p58,593
mail_parser-4.1.2.dist-info/RECORD,,
mail_parser-4.1.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mail_parser-4.1.2.dist-info/WHEEL,sha256=a7TGlA-5DaHMRrarXjVbQagU3Man_dCnGIWMJr5kRWo,91
mail_parser-4.1.2.dist-info/entry_points.txt,sha256=S4B4uexH5BjdILaoXDneCGD1SUMPlFExOrjQ71gdVUI,57
mail_parser-4.1.2.dist-info/top_level.txt,sha256=u9cZcjgm7wuGievUGrL7ujbIube_iATfYHJ7Chvr7Ho,11
mailparser/__init__.py,sha256=KFYgW7UVGN15sfrg9KB1m-6npolxo0SGpseQO8mwjeU,1031
mailparser/__main__.py,sha256=7POVFMKPBHoBGecn2cjgOsTbxsb5Gc7dJ71fctShESI,8869
mailparser/__pycache__/__init__.cpython-312.pyc,,
mailparser/__pycache__/__main__.cpython-312.pyc,,
mailparser/__pycache__/const.cpython-312.pyc,,
mailparser/__pycache__/core.cpython-312.pyc,,
mailparser/__pycache__/exceptions.cpython-312.pyc,,
mailparser/__pycache__/utils.cpython-312.pyc,,
mailparser/__pycache__/version.cpython-312.pyc,,
mailparser/const.py,sha256=QLssqFepChQnXUzKO7Cc2svP2U7hm9MBtHy4wMLUufQ,3148
mailparser/core.py,sha256=mOGxZmyrrddXdnpsMj3zcRWY_5Oq5i5F6H9k1A26bZo,24140
mailparser/exceptions.py,sha256=dJtMgR7h-sF2TjhRpcBm1k-J9HgPH5oiMFQCUKVp_P0,1410
mailparser/utils.py,sha256=YjhAiWzrmxctuTRl-S64WMbMfYz9bkDUIyFEULUD_Y4,16677
mailparser/version.py,sha256=n2ThF0jsFf5nLFkiIq-UDf-Rr_zYEXUHigGo2Z_5Vk4,671
