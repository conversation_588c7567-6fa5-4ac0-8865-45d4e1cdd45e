import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Stack, Tab, Tabs, Typography} from "@mui/material";
import {Link, useNavigate, useParams, useLocation} from "react-router-dom";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import * as React from "react";
import {SyntheticEvent, useEffect, useState} from "react";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import TabContent from "@components/TabContent";
import IconButton from "@mui/material/IconButton";
import {urls} from "@routes";
import {
	ArrowBack,
	CalendarMonth,
	Cancel,
	Delete,
	Done,
	DoneAll,
	Pause,
	PlayArrow,
	PlayCircle,
	Download,
} from "@mui/icons-material";
import CampaignSetup from "@pages/Campaigns/CampaignSetup";
import CampaignAnalytics from "@pages/Campaigns/CampaignAnalytics";
import {useSnackbar} from "notistack";
import {useDialogs} from "@toolpad/core";
import CampaignSettings from "@pages/Campaigns/CampaignSettings";
import CampaignLeads from "@pages/Campaigns/CampaignLeads";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import {DateTimePicker, LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterLuxon} from '@mui/x-date-pickers/AdapterLuxon'
import {DateTime} from 'luxon';
import { useNavigateBlocker } from "@components/UseNavigateBlocker";

type CampaignContactRow = {
	email_id: string;
	status: string;
	sent_on: string;
	reply: string;
	bad_email: boolean;
	unsubscribed: boolean;
	reason: string;
	attributes?: Record<string, string | number | boolean | null>;
};


export default function CampaignDetails() {
	const {campaignUID} = useParams();
	const {enqueueSnackbar} = useSnackbar();
	const dialogs = useDialogs();
	const navigate = useNavigate();

	const [tabValue, setTabValue] = useState<number>(0);

	const [campaignName, setCampaignName] = useState<string>("");
	const [campaignStatus, setCampaignStatus] = useState<string>("");
	const [campaignSetupHasUnsavedChanges, setCampaignSetupHasUnsavedChanges] = useState(false);
	const [
		campaignCustomStartDatetime,
		setCampaignCustomStartDatetime
	] = useState<DateTime | null>(null);
	const [selectedTimezone, setSelectedTimezone] = useState(
		Intl.DateTimeFormat().resolvedOptions().timeZone
		);
	const location = useLocation();

	useNavigateBlocker(campaignSetupHasUnsavedChanges, async () => {
		return await dialogs.confirm(
		'You have unsaved email changes. Are you sure you want to leave without saving?',
		{
			title: 'Unsaved Changes',
			cancelText: 'Stay',
			okText: 'Leave Anyway',
		}
		);
	});


	useEffect(() => {
		const handleBeforeUnload = (e: BeforeUnloadEvent) => {
			if (campaignSetupHasUnsavedChanges) {
				e.preventDefault();
				e.returnValue = ''; // Required for some browsers
			}
		};

		window.addEventListener('beforeunload', handleBeforeUnload);

		return () => {
			window.removeEventListener('beforeunload', handleBeforeUnload);
		};
	}, [campaignSetupHasUnsavedChanges]);


	const handleBackLink = async (): Promise<string | undefined> => {
		if (campaignSetupHasUnsavedChanges) {
			const cancelConfirmed = await dialogs.confirm(
			"You have unsaved email changes. Are you sure you want to leave without saving?",
			{
				title: "Unsaved Changes",
				cancelText: "Stay",
				okText: "Leave Anyway",
			}
			);
			if (!cancelConfirmed) return undefined;

			setCampaignSetupHasUnsavedChanges(false);
		}

		return location.state?.from === "SendingHistory"
			? urls["SendingHistory"]
			: urls["campaigns"];
		};

	// Page Title
	useEffect(() => {
    document.title = "Campaign Details - Deliveryman.ai";
  }, []);

	// Query to fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["getCampaignDetails"],
		queryFn: () => authenticateAndFetchData(`/campaigns/details?campaign_uid=${campaignUID}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	
	useEffect(() => {
		if (pageDataQuery.data) {
			setCampaignName(pageDataQuery.data.data["name"]);
			setCampaignStatus(pageDataQuery.data.data["status"]);
			if (pageDataQuery.data.data["time_zone"] && pageDataQuery.data.data["time_zone"] !== "UTC") {
				setSelectedTimezone(pageDataQuery.data.data["time_zone"]);
			}
			let datestring: string | null = pageDataQuery.data.data["custom_start_datetime"]
			if (datestring !== null) {
				const date = DateTime.fromISO(datestring, {
					zone: DateTime.local().zoneName,
				});
				setCampaignCustomStartDatetime(date);
			} else {
				setCampaignCustomStartDatetime(datestring)
			}
		}
	}, [pageDataQuery.data]);

	const handleTabChange = async (event: SyntheticEvent, newValue: number) => {
		if (tabValue === 2 && campaignSetupHasUnsavedChanges) {
			const cancelConfirmed = await dialogs.confirm(
				"You have unsaved email changes. Are you sure you want to leave without saving?",
				{
					title: "Unsaved Changes",
					cancelText: "Stay",
					okText: "Leave Anyway",
				}
			);
			if (!cancelConfirmed) return;

			setCampaignSetupHasUnsavedChanges(false);
		}
		setTabValue(newValue);
	};

	// Mutation to start campaign.
	const startCampaignMutation = useMutation({
		mutationKey: ["startCampaign"],
		mutationFn: () => authenticateAndPostData("/campaigns/start-campaign/", {
			campaign_uid: campaignUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			setCampaignStatus("running");
			navigate(0);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	const exportCampaignMutation = useMutation({
		mutationKey: ["exportCampaign", campaignUID],
		mutationFn: async () => {			
			const response = await authenticateAndFetchData(
			`/campaigns/campaign_export_data/?campaign_uid=${campaignUID}`,
			"blob",
			);    
		const blob = response.data;

		const url = window.URL.createObjectURL(blob);
		const link = document.createElement("a");
		link.href = url;
		link.setAttribute("download", `campaign_${campaignUID}.csv`);
		document.body.appendChild(link);
		link.click();
		link.remove();
		window.URL.revokeObjectURL(url);

		return true;
	},
	onSuccess: () => {
		enqueueSnackbar("CSV downloaded successfully!", { variant: "success" });
	},
	onError: (error: any) => {
		console.error(error);
		enqueueSnackbar("Error while exporting campaign.", { variant: "error" });
	},
	});


	// Mutation to resume campaign.
	const resumeCampaignMutation = useMutation({
		mutationKey: ["resumeCampaign"],
		mutationFn: () => authenticateAndPostData("/campaigns/resume-campaign/", {
			campaign_uid: campaignUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			setCampaignStatus("running");
			navigate(0);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Mutation to resume campaign.
	const pauseCampaignMutation = useMutation({
		mutationKey: ["pauseCampaign"],
		mutationFn: () => authenticateAndPostData("/campaigns/pause-campaign/", {
			campaign_uid: campaignUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			setCampaignStatus("paused");
			navigate(0);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Mutation to cancel campaign.
	const cancelCampaignMutation = useMutation({
		mutationKey: ["cancelCampaign"],
		mutationFn: () => authenticateAndPostData("/campaigns/cancel-campaign/", {
			campaign_uid: campaignUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			setCampaignStatus("cancelled");
			navigate(0);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function refetchPageData() {
		pageDataQuery.refetch().then();
	}

	function CampaignControls() {
		// --------- Start Campaign
		const StartCamapignButton = () => {
			const [open, setOpen] = useState(false);

			const handleClickOpen = () => {
				setOpen(true);
			};

			const handleClose = () => {
				setOpen(false);
			};

			const handleConfirm = () => {
				startCampaignMutation.mutate();
			}

			if (campaignStatus === "created") {
				return (
					<React.Fragment>
						<Button startIcon={<PlayArrow/>}
										variant={"contained"}
										color={"success"}
										onClick={handleClickOpen}
										disabled={startCampaignMutation.isPending}>
							Start
						</Button>
						<Dialog
							onClose={handleClose}
							aria-labelledby="start-campaign-dialog"
							open={open}
						>
							<DialogTitle sx={{m: 0, p: 2}} id="start-campaign-dialog">
								Start Campaign
							</DialogTitle>
							<DialogContent>
								<Typography variant={"body1"}>
									This will start your campaign immediately. Once started, you cannot:
								</Typography>
								<ul>
									<li>Add new leads/contacts to the campaign</li>
									<li>Change email content</li>
									<li>Add or remove sending domains</li>
									<li>Change email where you will receive campaign replies</li>
								</ul>
							</DialogContent>
							<DialogActions>
								<Button autoFocus onClick={handleClose}>
									Close
								</Button>
								<Button onClick={handleConfirm}>
									Proceed
								</Button>
							</DialogActions>
						</Dialog>
					</React.Fragment>
				)

			} else if (campaignStatus === "complete") {
				return (
					<Button variant={"contained"} color={"success"} disabled>
						<DoneAll/>&nbsp;&nbsp;Completed
					</Button>
				)
			} else {
				return (
					<Button variant={"contained"} color={"success"} disabled>
						<Done/>&nbsp;&nbsp;Running
					</Button>
				)
			}
		}

		const downloadCampaignCSV = (data : CampaignContactRow[]) => {
			if (!data || data.length === 0) {
				enqueueSnackbar("No data to export.", { variant: "error" });
				return;
			}

			const escapeCSV = (value) => {
				if (value == null) return "";
				const stringValue = String(value);
				return `"${stringValue.replace(/"/g, '""')}"`;
			};

			// Step 1: Extract all unique attribute keys
			const attributeKeys = new Set();
			data.forEach((row) => {
				if (row.attributes) {
					Object.keys(row.attributes).forEach((key) => attributeKeys.add(key));
				}
			});
			const attributeColumns = Array.from(attributeKeys);

			// Step 2: Build the header row
			const csvHeader = [
				...attributeColumns, "Email", "Status", "Sent On", "Reply", "Bad Email", "Unsubscribed", "Reason"
			];

			// Step 3: Map the data rows
			const csvData = [
				csvHeader,
				...data.map((row) => {
					const baseColumns = [
						escapeCSV(row.email_id),
						escapeCSV(row.status),
						escapeCSV(row.sent_on),
						escapeCSV(row.reply),
						escapeCSV(row.bad_email),
						escapeCSV(row.unsubscribed),
						escapeCSV(row.reason),
					];

					const attributeValues = attributeColumns.map((key) =>
						escapeCSV(row.attributes?.[key as keyof typeof row.attributes] ?? "")
					);


					return [...attributeValues, ...baseColumns];
				}),
			];

			// Step 4: Generate and download CSV
			const csvContent = csvData.map(e => e.join(",")).join("\n");
			const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
			const url = URL.createObjectURL(blob);
			const link = document.createElement("a");
			link.setAttribute("href", url);
			link.setAttribute("download", `${campaignUID}_Export.csv`);
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		};

		const handleExportClick = () => {
			exportCampaignMutation.mutate();
		};


		// --------- Schedule Campaign
		const ScheduleCamapignButton = (props: {
			campaignCustomStartDatetime: DateTime | null,
			refetchPageData: () => void,
		}) => {
			const [open, setOpen] = useState(false);
			const [
				datetime,
				setDatetime
			] = useState<DateTime | null>(props.campaignCustomStartDatetime);

			// Mutation to set custom start datetime
			const scheduleCampaignMutation = useMutation({
				mutationKey: ["scheduleCamapign"],
				mutationFn: () => authenticateAndPostData("/campaigns/scheduled-start/add/", {
					campaign_uid: campaignUID,
					start_datetime: datetime
				}),
				gcTime: 0,
				retry: retryFn,
				onSuccess: () => {
					enqueueSnackbar("Campaign start time updated successfully!", {
						variant: "success",
					});
					setOpen(false);
					refetchPageData();
				},
				onError: (error: ApiRequestFailed) => {
					enqueueSnackbar(error.data.message, {
						variant: "error",
					});
					console.error(error);
				}
			});

			const handleClickOpen = () => {
				setOpen(true);
			};

			const handleClose = () => {
				setOpen(false);
			};

			const handleConfirm = () => {
				if (datetime !== null) {
					scheduleCampaignMutation.mutate();
				} else {
					enqueueSnackbar("Please select a valid date and time.", {
						variant: "error",
					});
				}
			}

			return (
				<React.Fragment>
					<LocalizationProvider dateAdapter={AdapterLuxon}>
						<Button startIcon={<CalendarMonth/>}
										variant={"contained"}
										onClick={handleClickOpen}
										disabled={campaignStatus !== "created"}>
							Schedule
						</Button>
						<Dialog
							onClose={handleClose}
							aria-labelledby="view-reply-dialog"
							open={open}
						>
							<DialogTitle sx={{m: 0, p: 2}} id="view-reply-dialog">
								Campaign Schedule
							</DialogTitle>
							<DialogContent>
								<Typography variant={"body1"}>
									We'll start sending first batch of emails on this date and time.<br/>
									(NOTE: Selected value should be at least 5 minutes in future)
								</Typography>
								<Typography variant={"body1"} sx={{mt: 2, fontWeight: "bold"}}>
									Timezone: {selectedTimezone}
								</Typography>
								<DateTimePicker value={datetime} onChange={setDatetime} timezone={selectedTimezone}
																sx={{mt: 4}}/>
							</DialogContent>
							<DialogActions>
								<Button autoFocus onClick={handleClose}>
									Close
								</Button>
								<Button onClick={handleConfirm}>
									Confirm
								</Button>
							</DialogActions>
						</Dialog>
					</LocalizationProvider>
				</React.Fragment>
			)
		}

		// Export Campaign
		const ExportCampaignButton = () => {
			return (
					<Button variant={"contained"}
									color={"primary"}
									startIcon={<Download />}
									disabled={exportCampaignMutation.isPending}
									onClick={handleExportClick}
									>
						{exportCampaignMutation.isPending ? 'Exporting...' : 'Export'}
					</Button>
			)
		}

		// --------- Cancel Campaign
		const CancelCampaignButton = () => {
			console.log(campaignStatus);
			if (campaignStatus === "cancelled" || campaignStatus === "complete") {
				return (
					<Button variant={"contained"}
									color={"error"}
									disabled={true} startIcon={<Cancel/>}>
						Cancel
					</Button>
				)

			} else {
				return (
					<Button variant={"contained"}
									color={"error"}
									disabled={cancelCampaignMutation.isPending}
									startIcon={<Cancel/>}
									onClick={async () => {
										const cancelConfirmed = await dialogs.confirm(
											"Are you sure you want to cancel this campaign? All remaining schedules will be deleted " +
											"but you can still access campaign data.",
											{
												title: "Cancel Campaign",
												cancelText: "Cancel",
												okText: "Confirm",
											}
										);
										if (cancelConfirmed) {
											cancelCampaignMutation.mutate();
										}
									}}>
						{cancelCampaignMutation.isPending ?
							<>Cancelling...</> :
							<>Cancel</>}
					</Button>
				)
			}
		}

		// --------- Pause Campaign
		const PauseCampaignButton = () => {
			if (campaignStatus === "running") {
				return (
					<Button variant={"contained"}
									color={"warning"}
									disabled={pauseCampaignMutation.isPending}
									startIcon={<Pause/>}
									onClick={() => {
										pauseCampaignMutation.mutate();
									}}>
						{pauseCampaignMutation.isPending ?
							<>Pausing...</> :
							<>Pause</>}
					</Button>
				)
			} else {
				return (
					<Button variant={"contained"}
									color={"warning"}
									disabled={true}
									startIcon={<Pause/>}>
						Pause
					</Button>
				)
			}
		}

		// --------- Resume Campaign
		const ResumeCampaignButton = () => {
			if (campaignStatus !== "paused") {
				return (
					<Button variant={"contained"}
									color={"success"}
									disabled={true}
									startIcon={<PlayCircle/>}>
						Resume
					</Button>
				)

			} else {
				return (
					<Button variant={"contained"}
									color={"success"}
									disabled={resumeCampaignMutation.isPending}
									startIcon={<PlayCircle/>}
									onClick={() => {
										resumeCampaignMutation.mutate();
									}}>
						{resumeCampaignMutation.isPending ?
							<>Resuming...</> :
							<>Resume</>}
					</Button>
				)
			}
		}

		return (
			<Stack direction={"row"} spacing={2}>
				{["running", "paused", "created", "scheduled", "cancelled", "complete"].includes(campaignStatus) && <ExportCampaignButton/>}
				{campaignStatus === "created" && <ScheduleCamapignButton
					campaignCustomStartDatetime={campaignCustomStartDatetime}
					refetchPageData={refetchPageData}/>}
				{campaignStatus === "created" && <StartCamapignButton/>}
				{campaignStatus === "running" && <PauseCampaignButton/>}
				{campaignStatus === "paused" && <ResumeCampaignButton/>}
				{["running", "paused", "created", "scheduled"].includes(campaignStatus) && <CancelCampaignButton/>}
			</Stack>
		)
	}

	const ScheduleTimeAlert = () => {
		const [closeScheduleStartInfo, setCloseScheduleStartInfo] = useState(false);

		// Mutation to set custom start datetime
		const deleteCampaignScheduledStartMutation = useMutation({
			mutationKey: ["deleteCampaignScheduledStart"],
			mutationFn: () => authenticateAndPostData("/campaigns/scheduled-start/delete/", {
				campaign_uid: campaignUID,
			}),
			gcTime: 0,
			retry: retryFn,
			onSuccess: () => {
				enqueueSnackbar("Campaign scheduled start time removed successfully.", {
					variant: "success",
				});
				refetchPageData();
			},
			onError: (error: ApiRequestFailed) => {
				enqueueSnackbar(error.data.message, {
					variant: "error",
				});
				console.error(error);
			}
		});

		if (campaignStatus === "scheduled" && campaignCustomStartDatetime) {
			return <Alert severity="success" action={
				<Button color={"error"}
								onClick={() => {
									deleteCampaignScheduledStartMutation.mutate();
								}}
								disabled={deleteCampaignScheduledStartMutation.isPending}
								startIcon={<Delete/>}>
					DELETE SCHEDULE
				</Button>
			}>
				Campaign has been scheduled to start
				automatically on <b>{campaignCustomStartDatetime.toLocaleString(DateTime.DATETIME_FULL)}</b>
			</Alert>;

		} else if (campaignStatus === "created" && !closeScheduleStartInfo) {
			return <Alert severity="info" onClose={() => {
				setCloseScheduleStartInfo(true);
			}}>
				We have updated spintax format to the standard {'{word1|word2|word3}'}.
				All running campaigns will still work with the old format.
			</Alert>;
		} else {
			return null;
		}

		// if (campaignStatus !== "created") return null;
		//
		// if (campaignCustomStartDatetime === null) {
		// 	return <Alert severity="info">
		// 		Use <b>Schedule Campaign</b> to automatically start the campaign at given date and time or
		// 		use <b>Start Campaign</b> button to begin the campaign immediately.
		// 	</Alert>;
		// } else {
		//
		// }
	}

	function campaignStatusChipColor(status: string) {
		switch (status) {
			case "creating":
				return "warning";

			case "created":
				return "default";

			case "scheduled":
				return "primary";

			case "running":
				return "primary";

			case "complete":
				return "success";

			case "cancelled":
				return "error";

			case "paused":
				return "warning"

			default:
				return "default";
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<>
				{/* Campaign Name & Go Back button */}
				<Stack direction={"row"} alignItems={"center"} justifyContent={"space-between"}>
					<Stack direction={"row"} spacing={2} alignItems={"center"}>
						<IconButton onClick={async () => { const destination = await handleBackLink(); if (destination) { navigate(destination); }}}>
							<ArrowBack/>
						</IconButton>
						<Typography variant="h6" align={"left"}>Campaign: {campaignName}</Typography>
						<Chip size={"small"} color={campaignStatusChipColor(campaignStatus)} label={campaignStatus}/>
					</Stack>
					<CampaignControls/>
				</Stack>

				{/* Tabs */}
				<Box sx={{borderBottom: 1, borderColor: 'divider', mt: 1}}>
					<ScheduleTimeAlert/>
					<Tabs value={tabValue} onChange={handleTabChange} sx={{
						'& .MuiTabs-flexContainer': {
							flexWrap: 'wrap',
						},
					}}>
						<Tab label="Analytics"/>
						<Tab label="Contacts/Leads"/>
						<Tab label="Email Sequences"/>
						<Tab label="Campaign Settings"/>
					</Tabs>
				</Box>

				{/* Analytics Tab */}
				<TabContent value={tabValue} index={0}>
					<CampaignAnalytics campaignUID={campaignUID!}/>
				</TabContent>

				{/* Contact/Leads Tab */}
				<TabContent value={tabValue} index={1}>
					<CampaignLeads campaignUID={campaignUID!} camapignStatus={campaignStatus}/>
				</TabContent>

				{/* Email Sequences Tab */}
				<TabContent value={tabValue} index={2}>
					<CampaignSetup onUnsavedChangesChange={setCampaignSetupHasUnsavedChanges} />
				</TabContent>

				{/* Settings Tab */}
				<TabContent value={tabValue} index={3}>
					<CampaignSettings campaignUID={campaignUID!}/>
				</TabContent>
			</>

		)
	}
}
