# Generated by Django 5.1.2 on 2025-05-27 14:37

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0051_subscriptionplan_is_free_plan'),
    ]

    operations = [
        migrations.AddField(
            model_name='workspace',
            name='limits_last_renewed_on',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='workspace',
            name='monthly_emails_remaining',
            field=models.IntegerField(default=0),
        ),
    ]
