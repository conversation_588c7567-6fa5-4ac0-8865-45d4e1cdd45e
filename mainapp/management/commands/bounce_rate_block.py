import os
from datetime import datetime

from django.utils import timezone
from django.db.models import Count
from django.core.management.base import BaseCommand

from mainapp.email_messages import high_bounce_rate_email_body
from mainapp.models import Campaign, CampaignSchedule
from mainapp.tasks import send_email_task, cancel_campaign_task

"""
   Cornjob to check the bounce rate and cancel the campaign.
"""

class Command(BaseCommand):
    
    def handle(self, *args, **options):
        campaigns = Campaign.objects.filter(status="running")
        
        for campaign in campaigns:
            total_sent = CampaignSchedule.objects.filter(campaign=campaign, sent_on__isnull=False).count()
            if total_sent >= 1000:
                bounces = campaign.bounces or 0
                bounce_rate = (bounces / total_sent) * 100

                if bounce_rate > 5:
                    cancel_campaign_task.delay(campaign.uid)
                    campaign.is_campaign_bounce_blocked = True
                    campaign.save()
                    
                    user = campaign.workspace.user  
                    
                    send_email_task.delay(
                        to=user.email,
                        sender="<EMAIL>",
                        sender_name="Team DeliverymanAI",
                        subject=f"Campaign Cancelled – High Bounce Rate Detected",
                        body_html=high_bounce_rate_email_body(
                            username=user.username,
                            campaign=campaign.name,
                            campaign_link=os.environ["CE_APP_HOST_URL"] + "/campaigns",
                            current_year=str(datetime.now().year),
                        )
                    )
                    
                    
