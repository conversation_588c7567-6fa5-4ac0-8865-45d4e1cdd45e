import os
from datetime import datetime

from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.email_messages import domain_connection_reminder_email_message, campaign_creation_reminder_email_message
from mainapp.models import User
from mainapp.tasks import send_email_task


class Command(BaseCommand):
    def handle(self, *args, **options):
        for user in User.objects.filter(
                first_domain_connected_on__isnull=False,
                has_sent_campaign_creation_reminder_email=False
        ):
            if (timezone.now() - user.first_domain_connected_on).days >= 2:
                send_email_task.delay(
                    to=user.email,
                    sender="<EMAIL>",
                    sender_name="Team DeliverymanAI",
                    subject=f"Start your first campaign, {user.username}?",
                    body_html=campaign_creation_reminder_email_message(
                        username=user.username,
                        create_campaign_url=os.environ["CE_APP_HOST_URL"] + "/campaigns/create",
                        current_year=str(datetime.now().year),
                    )
                )
                user.has_sent_campaign_creation_reminder_email = True
                user.save()
                self.stdout.write(self.style.SUCCESS(f"[*] Eamil task created for {user.email}"))

        self.stdout.write("All Done!")
