import logging
import math
import random
from datetime import datetime, timezone, timedelta
from typing import List, Dict

from ColdEmailerBackend.settings import DEBUG, MAX_SENDING_LIMIT
from mainapp.models import CampaignEmailMessage, CampaignContact, EmailID, Campaign
from mainapp.pydantic_models import Schedule

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


class PatternFollowingScheduleGenerator:
    def __init__(self, campaign: Campaign):
        self._campaign: Campaign = campaign
        self._messages: List[CampaignEmailMessage] = list(campaign.campaignemailmessage_set.all())
        self._contacts: List[CampaignContact] = list(campaign.campaigncontact_set.all())
        self._emails: List[EmailID] = list(EmailID.objects.filter(
            email_subdomain__managed_subdomain__campaign=campaign
        ))
        # Shuffle the emails to prevent repeated resuse of same emails across multiple campaigns.
        random.shuffle(self._emails)
        # We add 0 at start (for the first email message) and pop the last one since we don't
        # need it (if there are more than one messages).
        self._days_gap = [0] + [msg.next_message_days for msg in self._messages]
        if len(self._days_gap) > 1:
            self._days_gap.pop(-1)

        # Initialize a list of sent email counter. Each index corresponds to a message in self._messages
        # Will be used to keep track of how many emails have been sent in total.
        self._emails_sent = [0] * len(self._messages)

        self._total_emails_to_send: int = len(self._messages) * len(self._contacts)

    @staticmethod
    def _increment_date(date: datetime, days_to_skip: List[str], days_gap: int):
        """
        Increments the given date. The first increment uses the days_gap value,
        and subsequent increments (if needed due to skipped days) are by one day.
        It skips any days that fall on a day of the week present in days_to_skip.

        :param date: The starting datetime object.
        :param days_to_skip: A list of strings representing names of days to skip (e.g., "Monday", "Tuesday").
        :param days_gap: The initial number of days to increment by.
        :return: The incremented datetime object, ensuring it's not a skipped day.
        """
        # Make everything lowercase.
        days_to_skip = [day.lower() for day in days_to_skip]

        # Dictionary to map weekday numbers to day names
        weekday_names = {
            0: "monday", 1: "tuesday", 2: "wednesday", 3: "thursday",
            4: "friday", 5: "saturday", 6: "sunday"
        }

        # First, increment by days_gap
        date += timedelta(days=days_gap)

        # Then, continuously increment by 1 day until a non-skipped day is found
        while True:
            current_day_name = weekday_names[date.weekday()]
            if current_day_name not in days_to_skip:
                break
            date += timedelta(days=1)

        return date

    def generate_schedules(self, days_to_skip=None) -> List[Schedule]:
        """
        Generates and returns schedules using provided list of EmailID model objects for this campaign.

        LOGIC:
        ------
        We'll loop till sum of emails sent across all messages is equal to total emails to be sent in this campaign.
        In each iteration we'll loop through the messages. Using days_gap value for this message, we'll find which
        date this email will fall on. For each campaign message, we also calculate the next email sending
        limit (capped). We still use the limit used for first message, for followups. But the final incremented
        value in a set will be used as limit for first message + followups of next set.
        """
        if days_to_skip is None:
            days_to_skip = []

        # Get current date with 15 mins additional time.
        # We will delay campaign to next day if less than 1 hour is remaining for the day.
        if 24 - datetime.now(tz=timezone.utc).hour <= 1:
            # Next day (1 minute past midnight)
            current_date = (datetime.now(tz=timezone.utc) + timedelta(days=1)).replace(
                hour=0,
                minute=15,
                second=0,
                microsecond=0
            )
        else:
            # Current date.
            current_date = datetime.now(tz=timezone.utc) + timedelta(minutes=15)

        # We'll hold the calculated schedules here.
        schedules: List[Schedule] = []

        set_number = 0

        # Calculate the total email sending limit.
        if self._campaign.emails_per_day > 0:
            email_limit: int = self._campaign.emails_per_day
        else:
            email_limit: int = 0
            for msub in self._campaign.sending_domains.all():
                email_limit += msub.email_limit

        # Cap to max sending limit defined in settings.
        email_limit = min(email_limit, MAX_SENDING_LIMIT, len(self._contacts))

        logger.debug(f"Email Sending Limit -> {email_limit}")

        # ------------- Create contact - email mapping -------------
        # We need to do this so we can handle all followups of each contact with the same campaign email.
        # We'll assign email to each contact in a round robin manner.
        # This will be stored in a dictionary where each key is the contact email and value is the sending
        # EmailID model object.
        contact_email_map: Dict = {}
        email_index = 0
        for contact in self._contacts:
            contact_email_map[contact.email_id] = self._emails[email_index]
            if email_index < (len(self._emails) - 1):
                email_index += 1
            else:
                email_index = 0

        # We'll loop until all schedules are generated.
        # No. of schedules to generate = no. of messages x no. of contacts
        while sum(self._emails_sent) < (len(self._messages) * len(self._contacts)):
            # A set is a group of first email message + their followups for a subset of contacts.
            # Each set will have unique subset of contacts.
            # Number of contacts in a set is capped by the daily email sending limit for this campaign.
            set_number += 1
            current_email_limit: int = email_limit

            # Calculate "seconds" between each email for the day.
            email_gap_seconds: int = math.floor(((24 - current_date.hour) * 60 * 60) / current_email_limit)
            # email_gap_seconds: int = 60

            for msg_index, msg in enumerate(self._messages):
                # This will only be used to mark the start of day.
                # current_date += timedelta(days=self._days_gap[msg_index])
                current_date = self._increment_date(current_date, days_to_skip, self._days_gap[msg_index])
                # This will be used to increment date using `email_gap_seconds`.
                schedule_date = current_date

                logger.debug(f"Set {set_number} - Message {msg_index} using email limit of {current_email_limit}")

                # Create schedules for each contact.
                # We'll rotate the emails pool in a round robin manner.
                contact_range_start = self._emails_sent[msg_index]
                contact_range_end = contact_range_start + current_email_limit
                contacts_to_send = self._contacts[contact_range_start:contact_range_end]
                for contact in contacts_to_send:
                    # NOTE: This is the pydantic model, not database model.
                    schedules.append(Schedule(
                        sending_email_id=contact_email_map[contact.email_id].id,
                        contact_id=contact.id,
                        message_id=msg.id,
                        date=schedule_date,
                    ))

                    # We will increase the current time based on calculated gap
                    # between each email for the day.
                    schedule_date += timedelta(seconds=email_gap_seconds)

                # Update emails sent for this message.
                self._emails_sent[msg_index] += len(contacts_to_send)

            # Move 1 day ahead to start with next set of emails. Start at 1 minute past midnight.
            # current_date += timedelta(days=1)
            current_date = self._increment_date(current_date, days_to_skip, 1)
            current_date = current_date.replace(
                hour=0,
                minute=1,
                second=0,
                microsecond=0
            )

            logger.debug(self._emails_sent)

        # Since we are locking domains to running campaign and we already checked for required amount of emails,
        # this should not happen unless there is some bug/mistake in above calculations.
        if len(schedules) < self._total_emails_to_send:
            raise Exception("Number of schedules does not match total emails to send.")

        # # Uncomment for debugging only.
        # with open("/Users/<USER>/Desktop/schedules.csv", "a") as csvfile:
        #     csvwriter = csv.writer(csvfile)
        #     rows = [["Date", "Sender", "Contact"]]
        #
        #     for schedule in schedules:
        #         email_address = EmailID.objects.get(id=schedule.sending_email_id).email_address
        #         contact_email = CampaignContact.objects.get(id=schedule.contact_id).email_id
        #         date = self._get_date_string(schedule.date)
        #         rows.append([date, email_address, contact_email])
        #
        #     csvwriter.writerows(rows)

        return schedules

    @staticmethod
    def _get_date_string(date: datetime):
        return date.strftime("%d %b %Y")
