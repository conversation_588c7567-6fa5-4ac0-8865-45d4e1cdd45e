# Generated by Django 5.1.2 on 2025-07-31 13:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0078_merge_20250729_1414'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlacklistAlertStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_count', models.IntegerField(default=0)),
                ('last_sent', models.DateField(blank=True, null=True)),
                ('subdomain', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='mainapp.managedsubdomain')),
            ],
        ),
    ]
