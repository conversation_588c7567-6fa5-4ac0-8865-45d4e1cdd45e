from dateutil.relativedelta import relativedelta
from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.models import Workspace, SubscriptionPlan


class Command(BaseCommand):
    def handle(self, *args, **options):
        free_plan = SubscriptionPlan.objects.get(is_free_plan=True)

        for workspace in Workspace.objects.filter(subscription_plan__isnull=True):
            workspace.subscription_plan = free_plan
            workspace.billing_period = "monthly"
            workspace.credits_remaining = free_plan.monthly_email_sending_quota
            workspace.next_renewal_date = timezone.now() + relativedelta(months=+1)
            workspace.save()
            self.stdout.write(f"[*] Workspace {workspace.name} (id: {workspace.id}) has been moved to free plan.")

        self.stdout.write("[*] All Done!")
