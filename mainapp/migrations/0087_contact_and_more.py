# Generated by Django 5.1.2 on 2025-08-12 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0086_user_is_trial_period_eligible'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(max_length=255, unique=True)),
                ('email_id', models.CharField(max_length=500)),
                ('attributes', models.J<PERSON>NField(default=dict)),
                ('bounced', models.BooleanField(default=False)),
                ('unsubscribed', models.BooleanField(default=False)),
            ],
        ),
        migrations.AddField(
            model_name='campaigncontact',
            name='imported_contact_list_contacts',
            field=models.ManyToManyField(to='mainapp.contact'),
        ),
        migrations.CreateModel(
            name='ImportedContactList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.CharField(max_length=255, unique=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('name', models.CharField(max_length=300)),
                ('status', models.CharField(default='uploading', max_length=100)),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.workspace')),
            ],
        ),
        migrations.AddField(
            model_name='contact',
            name='contact_list',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.importedcontactlist'),
        ),
    ]
