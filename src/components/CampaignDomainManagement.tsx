import { useState } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    <PERSON>,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    IconButton,
    Stack,
    Typography,
    Alert,
    CircularProgress,
} from '@mui/material';
import { Delete, Info } from '@mui/icons-material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { authenticateAndFetchData, authenticateAndPostData, retryFn, ApiRequestFailed } from '@lib/apis';
import { useSnackbar } from 'notistack';

interface Domain {
    id: number;
    domain: string;
    contacts_count: number;
    email_limit: number;
    status: string;
}

interface CampaignDomainsData {
    campaign_uid: string;
    campaign_name: string;
    current_domains: Domain[];
    available_domains: Domain[];
}

interface CampaignDomainManagementProps {
    campaignUID: string;
    onDomainRemoved?: () => void;
}

export default function CampaignDomainManagement({ campaignUID, onDomainRemoved }: CampaignDomainManagementProps) {
    const { enqueueSnackbar } = useSnackbar();
    const [removeDialogOpen, setRemoveDialogOpen] = useState(false);
    const [domainToRemove, setDomainToRemove] = useState<Domain | null>(null);

    // Query to fetch campaign domains
    const domainsQuery = useQuery({
        queryKey: ['campaignDomains', campaignUID],
        queryFn: () => authenticateAndFetchData(`/campaigns/domains/?campaign_uid=${campaignUID}`),
        gcTime: 0,
        retry: retryFn,
        refetchOnWindowFocus: false,
    });

    // Mutation to remove domain
    const removeDomainMutation = useMutation({
        mutationKey: ['removeCampaignDomain'],
        mutationFn: (domainId: number) => authenticateAndPostData('/campaigns/domains/remove/', {
            campaign_uid: campaignUID,
            domain_id: domainId,
        }),
        gcTime: 0,
        retry: retryFn,
        onSuccess: (data: any) => {
            enqueueSnackbar(data?.message || 'Domain removed successfully', { variant: 'success' });
            setRemoveDialogOpen(false);
            setDomainToRemove(null);
            domainsQuery.refetch();
            if (onDomainRemoved) {
                onDomainRemoved();
            }
        },
        onError: (error: ApiRequestFailed) => {
            console.error(error);
            enqueueSnackbar(error.data?.message || 'Failed to remove domain', { variant: 'error' });
        },
    });

    const handleRemoveDomain = (domain: Domain) => {
        setDomainToRemove(domain);
        setRemoveDialogOpen(true);
    };

    const confirmRemoveDomain = () => {
        if (domainToRemove) {
            removeDomainMutation.mutate(domainToRemove.id);
        }
    };

    if (domainsQuery.isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
                <CircularProgress />
            </Box>
        );
    }

    if (domainsQuery.isError) {
        return (
            <Alert severity="error">
                Failed to load campaign domains. Please try again.
            </Alert>
        );
    }

    const data: CampaignDomainsData = domainsQuery.data?.data || {
        campaign_uid: campaignUID,
        campaign_name: '',
        current_domains: [],
        available_domains: []
    };

    return (
        <Box>
            <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                    You can remove domains from paused campaigns. When a domain is removed,
                    all contacts assigned to that domain will be automatically redistributed
                    to the remaining domains.
                </Typography>
            </Alert>

            <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                Current Domains ({data.current_domains.length})
            </Typography>

            <Box sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' },
                gap: 2
            }}>
                {data.current_domains.map((domain) => (
                    <Card variant="outlined" key={domain.id}>
                        <CardContent>
                            <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                                <Box>
                                    <Typography variant="h6" component="div">
                                        {domain.domain}
                                    </Typography>
                                    <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                                        <Chip
                                            size="small"
                                            label={`${domain.contacts_count} contacts`}
                                            color="primary"
                                            variant="outlined"
                                        />
                                        <Chip
                                            size="small"
                                            label={`${domain.email_limit}/day limit`}
                                            color="secondary"
                                            variant="outlined"
                                        />
                                        <Chip
                                            size="small"
                                            label={domain.status}
                                            color={domain.status === 'active' ? 'success' : 'default'}
                                        />
                                    </Stack>
                                </Box>
                                <IconButton
                                    color="error"
                                    onClick={() => handleRemoveDomain(domain)}
                                    disabled={data.current_domains.length <= 1 || removeDomainMutation.isPending}
                                    title={data.current_domains.length <= 1 ?
                                        "Cannot remove the last domain" :
                                        "Remove domain from campaign"
                                    }
                                >
                                    <Delete />
                                </IconButton>
                            </Stack>
                        </CardContent>
                    </Card>
                ))}
            </Box>

            {data.current_domains.length <= 1 && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                    <Stack direction="row" alignItems="center" spacing={1}>
                        <Info />
                        <Typography variant="body2">
                            You cannot remove the last domain from a campaign.
                            At least one domain must remain.
                        </Typography>
                    </Stack>
                </Alert>
            )}

            {/* Remove Domain Confirmation Dialog */}
            <Dialog
                open={removeDialogOpen}
                onClose={() => setRemoveDialogOpen(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>Remove Domain from Campaign</DialogTitle>
                <DialogContent>
                    <DialogContentText>
                        Are you sure you want to remove <strong>{domainToRemove?.domain}</strong> from this campaign?
                    </DialogContentText>
                    <Alert severity="warning" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                            This action will:
                        </Typography>
                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                            <li>Remove the domain from the campaign</li>
                            <li>Redistribute {domainToRemove?.contacts_count} contacts to remaining domains</li>
                            <li>Update all future email schedules</li>
                        </ul>
                        <Typography variant="body2">
                            This action cannot be undone.
                        </Typography>
                    </Alert>
                </DialogContent>
                <DialogActions>
                    <Button
                        onClick={() => setRemoveDialogOpen(false)}
                        disabled={removeDomainMutation.isPending}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={confirmRemoveDomain}
                        color="error"
                        variant="contained"
                        disabled={removeDomainMutation.isPending}
                        startIcon={removeDomainMutation.isPending ? <CircularProgress size={16} /> : <Delete />}
                    >
                        {removeDomainMutation.isPending ? 'Removing...' : 'Remove Domain'}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}
