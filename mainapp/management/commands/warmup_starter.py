import os

import requests
from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.models import WarmupSchedule


class Command(BaseCommand):
    def handle(self, *args, **options):
        # Get current datetime object in UTC.
        current_time = timezone.now()

        # Fetch all schedules that can be sent now.
        schedules = WarmupSchedule.objects.filter(schedule_datetime__lt=current_time)

        # Make email warmup API call for all above schedules.
        for schedule in schedules:
            res = requests.post(
                url="https://warmup.deliveryman.ai/schedule-warmup/",
                headers={
                    "Content-Type": "application/json",
                    "X-API-Key": os.environ["CE_EMAIL_WARMUP_API_KEY"],
                },
                json={
                    "name": schedule.email.username,
                    "email": schedule.email.email_address,
                },
                timeout=60
            )

            if res.status_code == 200:
                # If successful, delete this schedule from db.
                self.stdout.write(f"[*] Warmup started "
                                  f"for {schedule.email.email_address} (schedule id: {schedule.id})")
                schedule.delete()

            else:
                # If not successful, we'll try again on next run.
                self.stdout.write(f"[x] Failed to make email warmup request. Status Code: {res.status_code}")

        self.stdout.write("[*] All Done!")
