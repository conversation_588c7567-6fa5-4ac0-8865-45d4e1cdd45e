{"eventType": "Click", "mail": {"timestamp": "2025-09-01T10:20:16.473Z", "source": "<PERSON> <<EMAIL>>", "sendingAccountId": "************", "messageId": "0109019904cab0d9-********-aec1-4b13-9790-fa1469ef2535-000000", "destination": ["<EMAIL>"], "headersTruncated": "False", "headers": [{"name": "From", "value": "<PERSON> <<EMAIL>>"}, {"name": "To", "value": "\"<EMAIL>\" <<EMAIL>>"}, {"name": "Reply-To", "value": "<EMAIL>"}, {"name": "Subject", "value": "Tracking feature in HTML emails"}, {"name": "Content-Type", "value": "text/html; charset=UTF-8"}, {"name": "Content-Transfer-Encoding", "value": "8bit"}, {"name": "List-Unsubscribe", "value": "<https://aicaller-frontend.abun.com/unsubscribe/?cid=campaign_f5e12174af2445598fd7e59a80171fd5&email=<EMAIL>>"}, {"name": "List-Unsubscribe-Post", "value": "List-Unsubscribe=One-Click"}, {"name": "Feedback-ID", "value": "campaign-241:user-1:campaign:emailid-10060"}, {"name": "X-Auto-Response-Suppress", "value": "All"}, {"name": "X-<PERSON>ys-Trusted-Sender", "value": "<EMAIL>"}, {"name": "X-MSys-Originating-IP", "value": "[************]"}, {"name": "MIME-Version", "value": "1.0"}], "commonHeaders": {"from": ["<PERSON> <<EMAIL>>"], "replyTo": ["<EMAIL>"], "to": ["\"<EMAIL>\" <<EMAIL>>"], "messageId": "0109019904cab0d9-********-aec1-4b13-9790-fa1469ef2535-000000", "subject": "Tracking feature in HTML emails"}, "tags": {"ses:source-tls-version": ["TLSv1.3"], "schedule_uid": ["campaign_contact_email_2412d600b4124fe5bc9bef0b2d028485"], "ses:operation": ["SendRawEmail"], "ses:configuration-set": ["my-first-configuration-set"], "ses:source-ip": ["************"], "ses:from-domain": ["s.arundev5.bfcmsaasdeal.xyz"], "ses:caller-identity": ["LambdaSendEmailRole"]}}, "click": {"timestamp": "2025-09-01T10:23:40.715Z", "ipAddress": "***************", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0", "link": "https://app.deliveryman.ai/campaigns/", "linkTags": "None"}}