import time
from typing import List

from dateutil.relativedelta import relativedelta
from django.core.management import BaseCommand
from django.utils import timezone

from mainapp.campaign import CampaignManager
from mainapp.models import CampaignSchedule


class Command(BaseCommand):
    """
    This management command creates schedules on AWS EventBridge for all emails to be sent within next hour.
    For example:
    At 7:49 PM -> all emails between 8:00 - 9:00 PM
    At 8:49 PM -> all emails between 9:00 - 10:00 PM
    At 9:49 PM -> all emails between 10:00 - 11:00 PM
    At 10:49 PM -> all emails between 11:00 PM - 00:00 AM
    At 11:49 PM -> all emails between 00:00 - 01:00 AM
    so on ....

    It needs to be run as a cronjob with the following rule:
    49 * * * *

    This will run it 10 minutes before every next hour so we have some gap in case it takes longer to schedule.
    AWS EventBridge scheduling does not work with dates past current time. So it's important to consider this.
    """

    def handle(self, *args, **options):
        # --- Step 1 --- Get the current time and find the next hour start and end.
        # For example: If the script is run at 19:41 then we'll get 20:00 and 21:00 as start and end respectively.
        current_time = timezone.now()
        next_hour_start = (current_time + relativedelta(hours=+1)).replace(minute=0, second=0, microsecond=0)
        next_hour_end = next_hour_start + relativedelta(hours=+1)

        # --- Step 2 --- Schedule all for the next hour.
        start = time.time()
        schedules: List[CampaignSchedule] = list(CampaignSchedule.objects.prefetch_related("campaign").filter(
            campaign__status="running",
            status="created",
            schedule_datetime__gte=next_hour_start,
            schedule_datetime__lt=next_hour_end,
        ))
        self.stdout.write(self.style.NOTICE(f"[INFO] Fetching schedules took {time.time() - start:.2f} seconds"))
        self.stdout.write(self.style.NOTICE(f"[INFO] Schedule Count: {len(schedules)}"))

        start = time.time()
        CampaignManager.bulk_create_aws_schedules(schedules)
        self.stdout.write(self.style.NOTICE(f"[INFO] Creating bulk schedules on AWS took: {time.time() - start:.2f} seconds"))

        # --- Step 3 --- Change all to "scheduled" status
        for schedule in schedules:
            self.stdout.write(f"{schedule.campaign.name} - {schedule.schedule_datetime.strftime('%d/%m/%Y %H:%M:%S')}")
            schedule.status = "scheduled"

        start = time.time()
        CampaignSchedule.objects.bulk_update(schedules, ["status"], batch_size=100)
        self.stdout.write(
            self.style.NOTICE(f"[INFO] Bulk updating schedule status took {time.time() - start:.2f} seconds"))

        self.stdout.write(self.style.SUCCESS("[*] All Done!"))
