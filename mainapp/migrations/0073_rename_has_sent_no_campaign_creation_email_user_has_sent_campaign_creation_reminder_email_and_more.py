# Generated by Django 5.1.2 on 2025-06-30 14:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0072_rename_has_sent_domain_not_connected_email_user_has_sent_domain_connect_reminder_email'),
    ]

    operations = [
        migrations.RenameField(
            model_name='user',
            old_name='has_sent_no_campaign_creation_email',
            new_name='has_sent_campaign_creation_reminder_email',
        ),
        migrations.AddField(
            model_name='user',
            name='first_domain_connected_on',
            field=models.DateTimeField(default=None, null=True),
        ),
    ]
