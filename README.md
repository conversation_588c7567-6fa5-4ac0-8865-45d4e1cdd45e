# Deliveryman.ai API Backend (Django)

### Project Setup Requirements:

- Python 3.12.x
- Django v5.1.2
- DRF v3.15.2
- Postgres v17 (see below)
- Redis

### Postgres Setup:

---

Use docker to easily set up the database:
```shell
docker run -d \
       --name coldemailer-postgres \
       -e POSTGRES_USER=coldemailer \
       -e POSTGRES_PASSWORD=coldemailer \
       -e POSTGRES_DB=coldemailer_db \
       -v <folder_path>:/var/lib/postgresql/data \
       -p 5432:5432 \
       postgres:17.0
```

Replace **<folder_path>** with full path to a folder on your system where you would lik all postgres data to be stored. Make sure permissions for this folder are correct as that can prevent postgres from starting.

Alternatively if you are using a local postgres installation, create the required database and users using the above values for username, password and database name manually.

### Redis Setup:

---

Use docker to easily set up redis:
```shell
docker run -d --name coldemailer-redis -p 6379:6379 redis
```

Alternateively you can use a local redis installation.

### AWS Services & Region Notes:

---

We use different regions for different environments to prevent cross-environment conflicts. They are as follows:
- Local Development: Mumbai (ap-south-1)
- Staging: Ohio (us-east-2)
- Production: North Virginia (us-east-1)

Following services are required for full functionality of DelvierymanAI:
- IAM Users, Roles and Policies
- SES
- Lambda functions
- SNS
- Eventbridge Scheduler
- Route53
- S3
- CloudWatch

#### IAM Users, Roles and Policies:

We have custom made Roles and Policies primarily used to provide our project and other aws services like Lambda, SNS, S3 etc. with all the required permissions to communicate and work with each other.

#### SES:

This is the primary service used by DeliverymanAI in order to set up a user's email sending domain, subdomains and email ids. It also handles email receiving and forwarding logic using S3, Lambda and SNS. We use their **Email receiving** feature for this.

#### Lambda:

Current we are using 2 lambda functions: one for sending email & one for forwarding replies. \
Both use **Python 3.13** and their source code is located in aws_lambda folder.

For adding any additional library to the lambda function we need to download the library instead of using pip install normally. This can be done by mmoving into the correct directory and running:
```
pip install <library_name> -t .
```
This will download all library files into your current directory. Since these files will be commited into repo, only add additional libraries when there's no other option so that we can keep the file count and project size as low as possible.

#### SNS:

We use SNS in SES **email receiving** feature. it's purpose is to notify our backend about any campaign replies and to take action on it like:
- Saving the reply message S3 key.
- Classifying type of reply.
- Handling bounced emails.

We don't do this on email forwarding lambda function so that we can keep its execution time as low as possible and reduce any chance of failiure in forwarding the email.


#### EventBridge Scheduler:

We create schedules over here for sending campaign emails. This calls the send email lambda function with all the requierd data. More info on our algorithm for schedule creation and management can be found in its respecive wiki/documentation.


#### Route53:

We use this to set up and manage user's domains to use during campaigns. We create all the records here necesary for creating subdomains and email ids. Route53 is a global service meaning it's shared between all of our environment regions.

#### S3:

We use S3 bucket in SES **Email receiving** feature to save the reply messages. We save the object id in our databse so we can retrieve the messages later when required.

#### CloudWatch:

Just used for logging Lambda and SNS services.


### Project Setup:

---

1. Clone project
2. Create a `.venv` python virtual environment in root of project.
3. Activate virtual environment and install libraries using `pip install -r requirements.txt`
4. Zip the contents of **email_forwarding** and **send_campaign_email** folders as 2 separate .zip files and save them somewhere outside of the project.
5. Create an AWS lambda function named **<your_name>-coldemailer-send-email** 
   1. Use Python runtime 3.13, X86_64 architecture and existing execution role **LambdaSendEmailRole**. 
   2. Once created, upload the send_email zip file.
   3. In Configuration tab, change:
      1. Timeout 30s
      2. Memory 128 MB
      3. Ephemeral Storage 512 MB
      4. Environment Variables > Add 2 variables. They have been listed below this section.
6. Create 2nd Lambda function named **<your_name>-coldemailer-email-forwarding** with same runtime and architecture, but use **LambdaSesForwarderRole** as the execution role.
   1. Once created, upload the email forwarding zip file.
   2. In Configuration tab, change:
      1. Timeout 2 min
      2. Memory 128 MB
      3. Ephemeral Storage 512 MB
      4. Environment Variables > Add 8 variables. They have been listed below this section.
7. Go To SNS and create a new topic:
    1. FIFO
    2. Name: **<your_name>-coldemailer-reply-received**
8. Go to the created SNS topic and update the following:
   1. Delivery Policy > Copy paste the json given below this section.
   2. Subscriptions > Create subscription > HTTPS protocol > Enter the webhook url **https://<your_tunnel>/webhook/reply-received/**
9. Go To SES > Email receiving > coldemailer-email-forwarding-ruleset > Create Rule:
   1. Name: **<your_name>-rule**
   2. Status: enabled.
   3. TLS required.
   4. Spam and virus scanning enabled.
   5. (Next)
   6. Add new recipient condition > Enter the domain assigned to you in the format **.domain.com** (dot in front is important)
   7. (Next)
   8. Add 3 actions:
      1. Deliver to Amazon S3 bucket - select `coldemailer-incoming-mails-dev` bucket.
      2. Invoke AWS Lambda function - select the forwarding lambda function you created.
      3. Publish to Amazon SNS topic - select the SNS topic you created.
   9. Review and create the rule.
10. Copy `.env.example` contents and create `.env` file in **ColdEmailerBackend** folder. Add all the variable values.
11. Run migrate command.
12. Run `python manage.py add_free_plan` to add a minimum required "free" subscription plan needed for the site to work. You can manually add other "paid" plans from admin if and when required (You will need access to a Stripe sandbox env for this).

#### Send Email Lambda Environment Variables:

1. get_email_status_url - https://<your_tunnel>/campaigns/get-schedule-email-status/
2. update_email_status_url - https://<your_tunnel>/campaigns/mark-email-sent/

#### Email Forwarding Lambda Environment Variables:

1. CE_EMAIL_WARMUP_API_KEY - Add the warmup api key here.
2. aws_mailer_daemon - <EMAIL>
3. bucket_name - coldemailer-incoming-mails-dev
4. fetch_campaign_account_email_url - https://<your_tunnel>/campaigns/get-campaign-account-email/
5. fetch_user_dest_addr_url - https://<your_tunnel>/campaigns/get-user-dest-email/
6. mark_bad_email_url - https://<your_tunnel>/campaigns/mark-bad-email/
7. save_email_s3_key_url - https://<your_tunnel>/schedule/save-email-s3-key/
8. unsubscribe_contact_url - https://<your_tunnel>/campaigns/unsubscribe/

#### SNS Delivery Policy:

```json
{
  "http": {
    "defaultHealthyRetryPolicy": {
      "minDelayTarget": 20,
      "maxDelayTarget": 120,
      "numRetries": 10,
      "numMaxDelayRetries": 0,
      "numNoDelayRetries": 0,
      "numMinDelayRetries": 0,
      "backoffFunction": "linear"
    },
    "disableSubscriptionOverrides": false,
    "defaultThrottlePolicy": {
      "maxReceivesPerSecond": 10
    },
    "defaultRequestPolicy": {
      "headerContentType": "application/json"
    }
  }
}
```

### Start Server:

---

Start the server using `python manage.py runserver 8000`

NOTE: we are using Daphne webserver for websocket support. So runserver will be using Daphne behind the scenes.

### Extra Steps After Setting Up:

---

Assuming frontend is also up and running, create your first user through signup and then using Django shell, mark this user as an `admin`. This will allow you to use this account to access restricted admin urls. Just set `is_admin` mode field on User model to True. 

You can also change country to India to show all pricing in INR.

### Tunnel:

---

You can use [localtunnel](https://theboroer.github.io/localtunnel-www/) to start and share the tunnel link. You will also need this for running secure (wss://) websockets and setting up stripe webhooks.

Start tunnel using `lt --port 3000 --subdomain <subdomain>`

Use below format for subdomain: \
**<your_first_name>-deliveryman-api**

### Schedule Generation Algorithm:

---

We only have one schedule generation method as of now named "Pattern Following Schedule Generation". It works by preserving the gap days between each email and their followups, while also following the days to skip, the per day limit and the amount of time remaining for the day.

**Overview:** \
We first fetch the pool of emails locally from database, that will be used to run the campaign. This email pool is shuffled so that we don't end up using the same email every time for every campaign. Next the total sending limit is calculated based on the following:
- User defined per day limit for the campaign.
- Total of all selected domain sending limits.
- Max cap defined by us.

Minimum value of these 3 is considered as the daily sending limit.

Next we create a **contact list - sending email** mapping.  This maps each contact to a single email id in our campaign email pool. This is to ensure that all the emails (first message + followups) for a given contact is sent through the same email address. It's also required to ensure emails are sent in the same thread if they have the same subject.

Now we go through each message in the campaign, and for each contact-message pair, we create a schedule object in our database. We calculate how to spread out all the `daily limit` amount of schedules across the day and store this date value in the schedule. This is done using bulk create and other optimizations as there can be millions of records to handle.

We then select all the schedules that can be sent in the next hour and create schedules on AWS using EventBridge scheduler. Eventbridge scheduler is responsible for firing off the Send Email lambda function when it's time to send the email.

### AWS Scheduler Cronjob:

---

Reason we have this is to be able to manage millions of schedules across every campaign. Without it we ran into a lot of timeouts, rate limits and other issues with creating schedules on AWS EventBridge.

As we only consider the first hour of emails when starting a campaign, we need a way to create the remaining schedules on AWS. This where the cronjob comes in.

This cronjob runs 10 minutes prior to every hour (ex. at 10:49, 11:49, 12:49, 13:49, 14:49 etc.) and selects schedules for the next hour. For example, if the job runs at 13:49, it will select all "non-scheduled" schedules from 14:00 to 15:00 and then create them on EventBridge.

This ensures that at a given time we only have to work with making 10-15 api calls even for a campaign where there are 100K+ schedules.

### Blacklist Check Cronjob:

---

A daily cronjob queues blacklist check tasks for all domains setup completed.

- **Script**: `python manage.py black_list_checker`
- **Runs At**: 00:00 UTC (5:30 AM IST)
- **Function**: Divides domains into batches of 100 and queues Celery tasks to check each batch for blacklist status.
- **Related Task**: `check_blacklist_batch_task` (Celery)
- **Emails Sent**:
  - If domain was removed from blacklisted
  - If domain is currently blacklisted

Make sure the Celery worker and scheduler are running for task execution.

### Email Warmup:

---

We have a warmup system for improving domain and email reputation. The warmup system is a service we built in-house and is shared by all environments (production, staging, dev). We have 2 cronjobs warmup_starter.py and warmup_scheduler.py which handles creation and making api calls to warmup service.

Source code for warmup system is housed in a different repo.

### Domain Redirection:

---

Users can redirect visitors to their connected domain to another target. For example if user has connected **foo.bar.com** using Deliveryman, they can make it so that all visitors to foo.bar.com and its subdomains are redirected to **helloworld.com**. This is done using another in-house service we have which uses Nginx + Ansible.

Source code for this is housed in a different repo.

### Subscription Management:

---

We'll use cronjobs to refill/renew the different usage credits. This will run every 5-10 mins and 
checks if 1 month has passed since their last limit renewal. If so resets the limit. Otherwise skips it.

### Subscription Plan behaviour: 

---

#### Upgrade
> Bill the upgraded plan immediately. Reset billing date to today. Cancel old plan. Rollover the current unused credits.

#### Downgrade
> Downgrade at the end of subscription. Cancel at the current plan till the end of subscription. Rollover unused credits to the next downgraded plan. 

#### Downgrade (free plan)
> If the customer downgrades to free plan, expire the credits at the end of current subscription. 

#### Cancel
> If the customer cancels, move to free plan & expire credits at the end of current suscription.

#### Credit rollover
> Credits rollover in paid plans. Credits do not roll over in free plan.

#### What will happen if they have a campaign running and their credit becomes 0?
> Pause all campaigns. Email customer about upgrading to a paid plan. If upgraded, show option to resume all campaigns.


### Stripe Event Handling

---

**customer.created** - Add stripe customer id to User model object. 

**customer.deleted** - Set stripe customer id as NULL in User model object.

**customer.subscription.created** - Move workspace to paid plan from free plan.

**customer.subscription.updated** - Move workspace to new plan (upgrade/downgrade).

**customer.subscription.deleted** - Move workspace to free plan and remove subscription id.

**invoice.payment_failed** - Send an email alert to user.
