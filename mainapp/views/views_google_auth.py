import json
import logging
import os
import pickle
import uuid
from typing import Dict

import google_auth_oauthlib.flow
import requests
from cryptography.fernet import Fernet
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from oauthlib.oauth2 import InvalidGrantError
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from ColdEmailerBackend.settings import DEBUG, GOOGLE_API_SCOPES
from mainapp.models import User, GooglePostmasterIntegration
from mainapp.responses import JsonResponseSuccess, JsonResponseBadRequest, JsonResponseServerError

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


def google_oauth_full_flow(user: User) -> str:
    flow = google_auth_oauthlib.flow.Flow.from_client_secrets_file(
        client_secrets_file=os.environ["CE_GOOGLE_API_CLIENT_SECRET"],
        scopes=GOOGLE_API_SCOPES
    )
    if DEBUG:
        flow.redirect_uri = "http://localhost:3000/google-auth/callback/"
    else:
        flow.redirect_uri = "https://app.deliveryman.ai/google-auth/callback/"

    google_auth_request_uid: str = "google-auth-" + str(uuid.uuid4())
    user.google_auth_request_uid = google_auth_request_uid
    user.save()

    state = {
        "source": user.email,
        "request_uid": google_auth_request_uid,
    }

    f = Fernet(os.environ["CE_GOOGLE_AUTH_STATE_KEY"])
    token = f.encrypt(json.dumps(state).encode()).decode("utf-8")

    authorization_url, state = flow.authorization_url(
        access_type='offline',
        include_granted_scopes='true',
        prompt='consent',
        state=token,
    )

    return authorization_url


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def start_google_auth_flow(request):
    """
    API to start google api service authentication flow. Returns the auth url. Redirect users to this url on frontend.
    If existing credentials data is found, renews that instead. No url is returned in this case.
    """
    user: User = request.user

    # If credentials is missing -> run full auth flow
    # If credentials are available and valid:
    # - If refresh token is available & credentials are expired -> renew the tokens.
    # Otherwise do the full auth flow.

    credentials_pickle: bytes | None = user.google_api_auth_creds

    if not credentials_pickle:
        # New google auth user.
        authorization_url: str = google_oauth_full_flow(user)
        return JsonResponseSuccess(data={
            "action": "redirect",
            "url": authorization_url,
        })

    else:
        # Has existing authentication.
        credentials: Credentials = pickle.loads(credentials_pickle)
        if not credentials.valid:
            if credentials.expired and credentials.refresh_token:
                # Renew the tokens.
                credentials.refresh(Request())
                return JsonResponseSuccess(data={
                    "action": "reauthenticated"
                })

            else:
                authorization_url: str = google_oauth_full_flow(user)
                return JsonResponseSuccess(data={
                    "action": "redirect",
                    "url": authorization_url,
                })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def google_auth_callback(request):
    """
    API to handle google authentication callback data.
    """
    user: User = request.user

    try:
        google_auth_code: str = request.query_params.get("code", None)
        state: str = request.query_params["state"]
        error: str = request.query_params.get("error", None)
        if not google_auth_code:
            logger.error("google_auth_callback() - Empty string in 'google_auth_code'")
            return JsonResponseBadRequest(data={"message": "Unable to complete authentication due to some issue. "
                                                           "Please try again later."})
    except KeyError as k:
        logger.critical(f"google_auth_callback() - Missing Key '{k}'")
        return JsonResponseBadRequest(data={"message": "Unable to complete authentication due to some issue. "
                                                       "Please try again later."})

    if error:
        # Usually means user rejected auth (pressed cancel button)
        logger.error(f"google_auth_callback() - Auth failed due to error: '{error}'. "
                     f"This can be due to user rejecting the consent prompt.")
        user.google_auth_request_uid = None
        user.save()
        return JsonResponseSuccess(data={
            "state": "error"
        })

    # Verify request origin and uid.
    f = Fernet(os.environ["CE_GOOGLE_AUTH_STATE_KEY"])
    state_data: Dict = json.loads(f.decrypt(state.encode()).decode("utf-8"))
    source: str = state_data["source"]
    request_uid: str = state_data["request_uid"]
    if request_uid != user.google_auth_request_uid:
        logger.debug(f"google_auth_callback() - source: {source}")
        logger.debug(f"google_auth_callback() - request_uid: {request_uid}")
        logger.debug("google_auth_callback() - Invalid request. source and request uid mismatch.")
        return JsonResponseBadRequest(data={
            "message": "Invalid Request: Your authentication session was either already completed or expired."
        })

    flow = google_auth_oauthlib.flow.Flow.from_client_secrets_file(
        client_secrets_file=os.environ["CE_GOOGLE_API_CLIENT_SECRET"],
        scopes=GOOGLE_API_SCOPES
    )
    if DEBUG:
        flow.redirect_uri = "http://localhost:3000/google-auth/callback/"
    else:
        flow.redirect_uri = "https://app.deliveryman.ai/google-auth/callback/"

    try:
        flow.fetch_token(code=google_auth_code)
        credentials = flow.credentials
    except InvalidGrantError as err:
        logger.error(err)
        return JsonResponseBadRequest(data={"message": "Invalid authentication or already authenticated."})
    except Exception as err:
        logger.error(err, exc_info=True)
        return JsonResponseServerError(data={"message": "Something went wrong. Please try again."})

    # Add credentials to user account and reset the request uid to prevent reuse of this callback.
    user.google_api_auth_creds = pickle.dumps(credentials)
    user.google_auth_request_uid = None
    user.save()

    # Initialize google postmaster integration.
    new_postmaster_integration = GooglePostmasterIntegration.objects.create()
    user.google_postmaster_integration = new_postmaster_integration
    user.save()

    return JsonResponseSuccess(data={
        "state": "success"
    })


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def google_auth_revoke(request):
    """
    Invalidates and removes this user's google oauth token + any related data on our end.
    """
    user: User = request.user

    if not user.google_api_auth_creds:
        return JsonResponseBadRequest(data={
            "message": "Google API services authentication missing or was already revoked."
        })

    # Revoke token.
    credentials = pickle.loads(user.google_api_auth_creds)
    res = requests.post('https://oauth2.googleapis.com/revoke',
                        params={'token': credentials.refresh_token},
                        headers={'content-type': 'application/x-www-form-urlencoded'})

    if (res.status_code != 200) and (res.status_code != 400):
        logger.error(res.text)
        logger.critical(f"Failed to revoke Google API auth with status code {res.status_code}.")
        return JsonResponseBadRequest(data={"message": "Failed to revoke Google authentication. "
                                                       "Please try again later"})

    # Remove from db.
    user.google_auth_request_uid = None
    user.google_api_auth_creds = None
    user.google_postmaster_integration = None
    user.save()

    return JsonResponseSuccess()
