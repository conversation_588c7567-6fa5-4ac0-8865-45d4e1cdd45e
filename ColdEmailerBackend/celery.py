import os
import cronitor.celery

from celery import Celery

from ColdEmailerBackend.settings import DEBUG

cronitor.api_key = os.environ["CE_CRONITOR_API_KEY"]


# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ColdEmailerBackend.settings")

app = Celery("ColdEmailerBackend")

app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Register Celery instance with cronitor to discover all tasks and automatically add monitoring.
if not DEBUG:
    cronitor.celery.initialize(app)
