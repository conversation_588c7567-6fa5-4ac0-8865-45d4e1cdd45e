import time
from typing import List

import boto3
import typer
from cloudflare import Cloudflare

app = typer.Typer()


@app.command(name="check")
def check():
    print("Typer is working!")


@app.command(name="add-ns-records")
def add_ns_records(zone_id: str):
    api_key: str = input("Enter Cloudflare API Key: ")
    api_email: str = input("Enter Cloudflare API Email: ")
    subdomain: str = input("Managed Subdomain: ")
    ns_record_count: int = int(input("How many NS records to add: "))
    ns_record_values: List[str] = []
    for i in range(ns_record_count):
        ns_record_values.append(input(f"NS Record {i + 1} Value: "))

    cloudflare_client = Cloudflare(
        api_key=api_key,
        api_email=api_email,
    )

    for value in ns_record_values:
        print(f"[*] Adding NS record {value} for {subdomain}...")
        cloudflare_client.dns.records.create(
            zone_id=zone_id,
            name=subdomain,
            type="NS",
            content=value,
        )

    print("[*] All Done!")


@app.command(name="send-email")
def send_email():
    region: str = input("AWS Region code (ex. us-east-1): ").strip()
    sender: str = input("Sender Email: ")
    recipient: str = input("Recipient Email: ")
    reply_to: str = input("Reply-To Email: ")
    subject: str = input("Subject: ")
    body: str = input("Message: ")
    unsub_value: str = f"unsubscribe@{sender.split("@")[1]}?subject=unsubscribe"

    # Initialize ses v2 service.
    aws_ses_v2 = boto3.client("sesv2", region_name=region)

    response = aws_ses_v2.send_email(
        FromEmailAddress=sender,
        Destination={'ToAddresses': [recipient]},
        ReplyToAddresses=[
            reply_to,
        ],
        Content={
            'Simple': {
                'Subject': {'Data': subject},
                'Body': {
                    'Text': {'Data': body},
                },
                'Headers': [
                    {
                        'Name': "List-Unsubscribe",
                        'Value': unsub_value,
                    },
                ]
            }
        },
    )
    print(response["MessageId"])
    return response


@app.command(name="set-default-config-set")
def set_default_config_set():
    region: str = input("AWS Region code (ex. us-east-1): ").strip()
    config_set_name: str = input("Configuration Set Name: ").strip()

    # Initialize ses v2 service.
    aws_ses_v2 = boto3.client("sesv2", region_name=region)

    # Initialize variables for pagination
    all_identities: List[str] = []
    next_token: str | None = None

    # Fetch all identities.
    while True:
        params = {'PageSize': 1000}
        if next_token:
            params['NextToken'] = next_token

        response = aws_ses_v2.list_email_identities(**params)
        all_identities.extend([identity['IdentityName'] for identity in response['EmailIdentities']])

        # Check if there are more identities to fetch
        next_token = response.get('NextToken')
        if not next_token:
            break

    for identity in all_identities:
        try:
            aws_ses_v2.put_email_identity_configuration_set_attributes(
                EmailIdentity=identity,
                ConfigurationSetName=config_set_name,
            )
            print(f"[*] {identity} updated successfully!")
            time.sleep(0.5)  # To prevent rate limit.
        except aws_ses_v2.exceptions.NotFoundException as err:
            print(f"ERROR: {err}")
            print(f"[x] Failed to set config set: Could not find config set with name: {config_set_name}")


if __name__ == "__main__":
    app()
