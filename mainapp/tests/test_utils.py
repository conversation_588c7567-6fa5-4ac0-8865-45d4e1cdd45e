from unittest import TestCase

from mainapp.utils import csv_rows_to_dict


class UtilsTest(TestCase):
    def test_csv_rows_to_dict(self):
        headers = ["key1", "key2", "key3"]
        data = [
            ["value11", "value12", "value13"],
            ["value21", "value22", "value23"],
            ["value31", "value32", "value33"],
        ]
        result = csv_rows_to_dict(data, headers)
        self.assertListEqual(result, [
            {"key1": "value11", "key2": "value12", "key3": "value13"},
            {"key1": "value21", "key2": "value22", "key3": "value23"},
            {"key1": "value31", "key2": "value32", "key3": "value33"},
        ])
