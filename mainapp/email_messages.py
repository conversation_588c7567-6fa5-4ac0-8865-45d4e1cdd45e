from typing import List

jd_signature = """–<br/>
<PERSON><PERSON><br/>
Co-founder<br/>
deliveryman.ai<br/>
<br/>
P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don’t judge us yet. 
"""

noreply_signature = """— {|Team DeliverymanAI|The DeliverymanAI Team|Team at DeliverymanAI|From the DeliverymanAI team|Your team at DeliverymanAI|Regards, Team DeliverymanAI|}<br/>
<br/>
P.S. This is an automated email. Reach out via live chat if you need help!"""


def reset_password_email_message(username: str, reset_link: str):
    return f"""Hello {username}<br/>  
<br/>
We received your request to reset your password.<br/> 
<br/>
Your password reset link will expire in 24 hours:<br/> 
<br/>
Reset Your Password: {reset_link}<br/>
<br/>
If your link has expired, you can request a new one here: https://app.deliveryman.ai/reset-password<br/>
<br/>
Best Regards,<br/>
Team DeliverymanAI<br/>
Deliveryman.ai"""


def plan_renewal_paused_campaigns_email_message(username: str, campaign_names: List[str], workspace_name: str):
    joined_names = "<br/>".join(campaign_names)

    return f"""Hi {username},<br/>
<br/>
You have the following paused campaigns in your Deliveryman.ai workspace '{workspace_name}':<br/>
<br/>
{joined_names}<br/>
<br/>
You can login into your account & resume your campaigns or create new campaigns.<br/> 
<br/>
Best Regards,<br/>
Team DeliverymanAI<br/>
Deliveryman.ai"""


def new_signup_email_message(username: str, email: str) -> str:
    return f"""New Signup Alert:<br/>
<br/>
Name: {username}<br/>
Email: {email}<br/>"""


def signup_onboarding_email_message(username: str, dashboard_url: str, current_year: str) -> str:
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Thanks for signing up for DeliverymanAI.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">I am Junaid Ansari, co-founder of DeliverymanAI (& a few other tools with my team).
            </p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">You're one of the first to use a better way to send cold emails & actually land in
                the inbox.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Here's how to get started:</p>
            <ol>
                <li style="padding: 0.5em 0 0.5em 0;"><b>Connect your domain</b> - we handle the warmup</li>
                <li style="padding: 0.5em 0 0.5em 0;"><b>Import your leads</b> - we create sending emails for you.</li>
                <li style="padding: 0.5em 0 0.5em 0;"><b>Schedule your first campaign</b> - and you're live.</li>
            </ol>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Need help? Just reply to this email or book a quick setup call.</p>
            <p class="text-normal regards" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 25px;color: rgb(83, 83, 83);margin-top: 35px;">
                –<br>
                Junaid Ansari<br>
                Co-founder<br>
                Deliveryman.ai
            </p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in
                spam, don't judge us yet.</p>
            <a href="{dashboard_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                Get Started
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def domain_connected_email_message(username: str, domain_name: str, warmup_completion_date: str, dashboard_url: str,
                                   current_year: str) -> str:
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Your domain {domain_name} is connected successfully.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">We've started building a solid sender reputation for your domain.</p>
            <div style="padding: 0.75em; border: solid 1px rgb(240, 240, 240); border-radius: 6px; background-color: rgb(251, 251, 251);">
                <p><b>No action needed from you right now.</b></p>
                <p>The domain reputation building will finish on:</p>
                <p><b>{warmup_completion_date}</b></p>
                <p>We'll email you once it is completed.</p>
            </div>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in
                spam, don't judge us yet.</p>
            <a href="{dashboard_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                View Dashboard
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def domain_connection_reminder_email_message(username: str, connect_domain_url: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">We noticed you haven't connected your domain yet.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">This is the first step to start sending cold emails that reach the inbox.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);"><b>Here's how to do it:</b></p>
            <div style="padding: 0.75em; border: solid 1px rgb(240, 240, 240); border-radius: 6px; background-color: rgb(251, 251, 251);">
                <ol style="line-height: 1.2;">
                    <li style="padding: 0.5em 0 0.5em 0;">Login to your DeliverymanAI account.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Click on "Email Sending Domain" > "Add New Domain".</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Add your domain name.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Update NS records on your DNS provider dashboard (Cloudflare, Porkbun, Namecheap etc.)</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Add sender first & last name to generate more email IDs.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">You are all set to Create a Campaign.</li>
                </ol>
            </div>
            <a href="{connect_domain_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                Connect Domain
            </a>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Need help? Just reach out to us via live chat.</p>
            <p class="text-normal regards" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 25px;color: rgb(83, 83, 83);margin-top: 35px;">
                –<br>
                Junaid Ansari<br>
                Co-founder<br>
                Deliveryman.ai
            </p>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def domain_warmed_up_email_message(username: str, create_campaign_url: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hey, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Great news! Your domain reputation is built up and ready to send campaigns.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Head to your dashboard to create your first cold email sequence.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
            <a href="{create_campaign_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                Create Campaign
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def campaign_creation_reminder_email_message(username: str, create_campaign_url: str, current_year: str):
    return f"""<html style="background-color: #f0f0f0;">
<body style="background-color: #f0f0f0;">
    <div class="container" style="width: 100%;height: auto;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi {username},</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Your domain is built up, but you haven't started a campaign yet.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);"><b>Here's how to create your first one:</b></p>
            <div style="padding: 0.75em; border: solid 1px rgb(240, 240, 240); border-radius: 6px; background-color: rgb(251, 251, 251);">
                <ol style="line-height: 1.2;">
                    <li style="padding: 0.5em 0 0.5em 0;">Log in to your Deliveryman account.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Click on "Campaigns" > "Create New Campaign".</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Give a name to your campaign.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Upload the list of leads in CSV format & select the column of email address of the leads.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Power users can control how many emails you want to send per day by modifying the emails per day option.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Add the email that you want to receive the campaign replies on.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Select the domain that you wish to send emails from.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Click on the "Email Sequences" tab & add your email sequences.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">You are all set to schedule or start sending the email campaign.</li>
                </ol>
            </div>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">
                <b>Note:</b> If your domain is still under reputation building, you will not be able to start email campaigns 
                immediately, but you can schedule the campaign to start on a future date.
            </p>
            <p class="text-normal regards" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 25px;color: rgb(83, 83, 83);margin-top: 35px;">
                –<br>
                Junaid Ansari<br>
                Co-founder<br>
                Deliveryman.ai
            </p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in
                spam, don't judge us yet.</p>
            <a href="{create_campaign_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                Connect Domain
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def campaign_start_email_message(username: str, leads_count: int, sequence_count: int, completion_date: str,
                                 campaign_details_url: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Your campaign has started & emails are being sent.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">We'll follow the set schedule & deliver smartly.</p>
            <div style="padding: 0.75em; border: solid 1px rgb(240, 240, 240); border-radius: 6px; background-color: rgb(251, 251, 251);">
                <div style="display: flex; flex-direction: row; justify-content: space-between; padding-left: 1em; padding-right: 1em;">
                    <p><b>Total number of leads:</b></p>    
                    <p>{leads_count}</p>
                </div>
                <div style="display: flex; flex-direction: row; justify-content: space-between; padding-left: 1em; padding-right: 1em;">
                    <p><b>Number of Sequences:</b></p>    
                    <p>{sequence_count}</p>
                </div>
                <div style="display: flex; flex-direction: row; justify-content: space-between; padding-left: 1em; padding-right: 1em;">
                    <p><b>Completion date:</b></p>    
                    <p>{completion_date}</p>
                </div>
            </div>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Track performance in your dashboard real-time.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
            <a href="{campaign_details_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                View Campaign Dashboard
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def campaign_completed_email_message(username: str, emails_sent_count: int, reply_rate: str, bounce_rate: str,
                                     campaign_details_url: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Your campaign has just wrapped up.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Here's how it performed:</p>
            <div style="padding: 0.75em; border: solid 1px rgb(240, 240, 240); border-radius: 6px; background-color: rgb(251, 251, 251);">
                <div style="display: flex; flex-direction: row; justify-content: space-between; padding-left: 1em; padding-right: 1em;">
                    <p><b>Total emails sent:</b></p>    
                    <p>{emails_sent_count}</p>
                </div>
                <div style="display: flex; flex-direction: row; justify-content: space-between; padding-left: 1em; padding-right: 1em;">
                    <p><b>Reply rate:</b></p>    
                    <p>{reply_rate}%</p>
                </div>
                <div style="display: flex; flex-direction: row; justify-content: space-between; padding-left: 1em; padding-right: 1em;">
                    <p><b>Bounce rate:</b></p>    
                    <p>{bounce_rate}%</p>
                </div>
            </div>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">You can view more details in your dashboard.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
            <a href="{campaign_details_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                View Campaign Dashboard
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def campaign_paused_email_message(username: str, campaign_name: str, campaign_details_url: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi {username},</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Your campaign "{campaign_name}" has been paused.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">This could be because of:</p>
            <div style="padding: 0.75em; border: solid 1px rgb(240, 240, 240); border-radius: 6px; background-color: rgb(251, 251, 251);">
                <ul>
                    <li style="padding: 0.5em 0 0.5em 0;">Campaign was manually paused.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Low domain health.</li>
                    <li style="padding: 0.5em 0 0.5em 0;">Email credits have exhausted.</li>
                </ul>
            </div>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Check you campaign dashboard for more details.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
            <a href="{campaign_details_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                View Campaign Dashboard
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def campaign_resumed_email_message(username: str, campaign_name: str, campaign_details_url: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi {username},</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Your campaign "{campaign_name}" is now live again.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Eamils will resume sending as per your schedule.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Track progress from your campaign dashboard.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
            <a href="{campaign_details_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                View Campaign Dashboard
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def credits_exhausted_email_message(username: str, plan_page_url: str, workspace_name: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hey {username},</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">You've used up all your email credits for workspace "{workspace_name}" on DeliverymanAI.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);"><b>All active campaigns have been paused to avoid delivery issues.</b></p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">To keep things running, upgrade your plan now:</p>
            <a href="{plan_page_url}" target="_blank" rel="noreferrer" class="link-button" style="margin-top: 20px;margin-bottom: 10px;display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;">
                View Campaign Dashboard
            </a>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Need help choosing a plan? Just reach out via live chat and we'll guide you.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def approval_request_email_message(username: str, email: str, subject: str, body: str):
    return f"""<b>Approval request from {username} ({email}):</b><br/>
<br/>
<b>Subject</b>: {subject}<br/>
<br/>
<b>Body</b>:<br/>
<pre style="white-space: pre-wrap; word-wrap: break-word;">
{body}
</pre>"""


def approval_request_passed_email_message(username: str, campaign_name: str, campaign_dashboard_url: str, current_year: str):
    return f"""<html>
<head>
</head>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Good news! Your email campaign "{campaign_name}" has been reviewed and approved.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">You're all set to go! You can now schedule or launch the campaign from your dashboard.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">We'll continue to monitor your campaign for deliverability and domain health.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Wishing you high opens and great replies.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Team DeliverymanAI</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
            <a href="{campaign_dashboard_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                View Campaign Dashboard
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def approval_request_rejected_email_message(username: str, campaign_name: str, campaign_dashboard_url: str, current_year: str):
    return f"""<html>
<head>
</head>
<body>
    <div class="container" style="width: 100%;height: 100%;background-color: #f0f0f0;padding: 40px 0 40px 0;font-family: sans-serif;">
        <div class="card" style="display: block;width: auto;max-width: 500px;margin: auto;height: auto;background-color: white;padding: 50px;border-radius: 12px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%;height: auto;text-align: center;margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block;width: 50%;height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold;font-family: sans-serif;font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">We've reviewed your email message in campaign "{campaign_name}", but it couldn't be approved.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">To move forward, review the campaign content and fix all problematic words/sentences.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">You can also reach out via live chat for assistance.</p>
            <p class="text-normal footnote" style="font-weight: normal;font-family: sans-serif;font-size: 0.9em;line-height: 20px;color: rgb(83, 83, 83);">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don't judge us yet.
            </p>
            <a href="{campaign_dashboard_url}" target="_blank" rel="noreferrer" class="link-button" style="display: inline-block;padding: 10px 20px 10px 20px;background-color: #3b82f6;color: white;text-decoration: none;border-radius: 5px;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);font-family: sans-serif;margin-top: 20px;">
                View Campaign Dashboard
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif;color: #a6a6a6;text-align: center;margin-top: 30px;">
            @{current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""

def domain_blacklisted_removed_email_message(username: str, domain_name: str, campaign_url: str, current_year: str) -> str:
    return f"""<html>
<body>
    <div class="container" style="width: 100%; height: 100%; background-color: #f0f0f0; padding: 40px 0; font-family: sans-serif;">
        <div class="card" style="display: block; width: auto; max-width: 500px; margin: auto; background-color: white; padding: 50px; border-radius: 12px; box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%; text-align: center; margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block; width: 50%; height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold; font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-weight: normal; font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                Good news! Your domain <strong>{domain_name}</strong> has been successfully removed from all detected blacklists.
            </p>
            <p class="text-normal" style="font-weight: normal; font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                This means your sender reputation is now in a much healthier state, and your emails are less likely to land in spam.
            </p>
            <p class="text-normal" style="font-weight: normal; font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                We’ll continue to monitor your domain and alert you if anything changes.
            </p>
            <p class="text-normal" style="font-weight: normal; font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                You can now go ahead & resume or launch your campaigns confidently.
            </p>
            <p class="text-normal regards" style="font-size: 1em; line-height: 20px; color: rgb(83, 83, 83); margin-top: 35px;">
                Team DeliverymanAI
            </p>            
            <a href="{campaign_url}" target="_blank" rel="noreferrer" class="link-button"
                style="display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px; box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25); font-family: sans-serif; margin-top: 20px;">
                View Campaigns
            </a>
        </div>
        <p class="footer-text" style="font-family: sans-serif; color: #a6a6a6; text-align: center; margin-top: 30px;">
            © {current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def domain_blacklisted_email_message(username: str, domain_name: str, blacklisted: str, current_year: str) -> str:
    return f"""<html>
<body>
    <div class="container" style="width: 100%; height: 100%; background-color: #f0f0f0; padding: 40px 0; font-family: sans-serif;">
        <div class="card" style="display: block; width: auto; max-width: 500px; margin: auto; background-color: white; padding: 50px; border-radius: 12px; box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%; text-align: center; margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block; width: 50%; height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold; font-size: 1.2em;">Hi, {username}!</p>
            <p class="text-normal" style="font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                We’ve detected that your connected domain <strong>{domain_name}</strong> is currently listed on the following blacklist(s):
            </p>
            <ul style="padding-left: 20px; color: rgb(83, 83, 83); font-size: 1em; line-height: 26px;">
                {blacklisted}
            </ul>
            <p class="text-normal" style="font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                This can seriously affect your email deliverability & may cause your emails to land in spam.
            </p>
            <p class="text-normal" style="font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                We recommend resolving this as soon as possible to protect your sender reputation.
            </p>
            <p class="text-normal" style="font-size: 1em; line-height: 30px; color: rgb(83, 83, 83);">
                You can check removal steps directly with the blacklist providers, or reach out to us if you need guidance.
            </p>
            <p class="text-normal regards" style="font-size: 1em; line-height: 20px; color: rgb(83, 83, 83); margin-top: 35px;">
                Team DeliverymanAI
            </p>            
        </div>
        <p class="footer-text" style="font-family: sans-serif; color: #a6a6a6; text-align: center; margin-top: 30px;">
            © {current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def account_email_verification_email_body(username: str, verify_url: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%; height: 100%; background-color: #f0f0f0; padding: 40px 0; font-family: sans-serif;">
        <div class="card" style="display: block; width: auto; max-width: 500px; margin: auto; background-color: white; padding: 50px; border-radius: 12px; box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%; text-align: center; margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block; width: 50%; height: auto;">
            </div>
            <p class="text-bold" style="font-weight: bold; font-size: 1.2em;">Hey {username},</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Thanks for signing up with DeliverymanAI.</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">You're just one step away from sending smarter cold emails that land in inboxes (not spam folders).</p>
            <p class="text-normal" style="font-weight: normal;font-family: sans-serif;font-size: 1em;line-height: 30px;color: rgb(83, 83, 83);">Click the button below to verify your email & unlock all features.</p>
            <a href="{verify_url}" target="_blank" rel="noreferrer" class="link-button"
                style="display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px; box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25); font-family: sans-serif; margin-top: 20px;">
                Verify My Account
            </a>
            <p style="font-size: 0.95em; line-height: 24px; color: #535353; margin-top: 30px;">
                If the button doesn't work, just copy and paste this link into your browser:
            </p>
            <p style="font-size: 0.95em; line-height: 24px; color: #3b82f6; word-break: break-all;">
                {verify_url}
            </p>
            <p class="text-normal regards" style="font-size: 1em; line-height: 20px; color: rgb(83, 83, 83); margin-top: 35px;">
                Team DeliverymanAI
            </p>
            <p class="text-normal regards" style="font-size: 1em; line-height: 20px; color: rgb(83, 83, 83); margin-top: 35px;">
                P.S. This email was sent with G-suite, not DeliverymanAI. If it lands in spam, don’t judge us yet.
            </p>
        </div>
        <p class="footer-text" style="font-family: sans-serif; color: #a6a6a6; text-align: center; margin-top: 30px;">
            © {current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def high_bounce_rate_email_body(username: str, campaign: str, campaign_link: str, current_year: str):
    return f"""<html>
<body>
    <div class="container" style="width: 100%; height: 100%; background-color: #f0f0f0; padding: 40px 0; font-family: sans-serif;">
        <div class="card" style="display: block; width: auto; max-width: 500px; margin: auto; background-color: white; padding: 50px; border-radius: 12px; box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);">
            <div class="img-container" style="width: 100%; text-align: center; margin-bottom: 40px;">
                <img src="https://deliveryman.ai/wp-content/uploads/2025/06/dm-logo-circle.png" style="display: inline-block; width: 50%; height: auto;">
            </div>                    
            <p style="font-weight: bold; font-size: 1.2em; margin-bottom: 20px;">Hi {username},</p>            
            <p style="font-size: 1em; line-height: 30px; color: #535353;">
                We had to cancel your ongoing campaign "<strong>{campaign}</strong>".
            </p>
            <p style="font-size: 1em; line-height: 30px; color: #535353;">
                The campaign’s bounce rate exceeded <strong>5%</strong>, which can harm your domain reputation &amp; email deliverability.
            </p>
            <p style="font-size: 1em; line-height: 30px; color: #535353;">
                Before sending any new campaigns, please <strong>clean &amp; verify your email list</strong> to ensure all recipients are valid &amp; active.
            </p>
            <p style="font-size: 1em; line-height: 30px; color: #535353;">
                You should use a reputable email verification service to do this.
            </p>
            <p style="font-size: 1em; line-height: 30px; color: #535353;">
                Repeated violations of our bounce rate policy may result in your DeliverymanAI account being permanently banned.
            </p>
            <p style="font-size: 1em; line-height: 30px; color: #535353;">
                Once your list is clean, you can start a fresh campaign.
            </p>
            <a href="{campaign_link}" target="_blank" rel="noreferrer"
               style="display: inline-block; padding: 12px 24px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px; 
               box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25); font-family: sans-serif; margin-top: 20px; font-weight: bold;">
               Go to Campaigns
            </a>
            <p style="font-size: 1em; line-height: 20px; color: #535353; margin-top: 35px;">
                Team DeliverymanAI
            </p>            
        </div>
        <p style="font-family: sans-serif; color: #a6a6a6; text-align: center; margin-top: 30px;">
            © {current_year} DeliverymanAI. All rights reserved.
        </p>
    </div>
</body>
</html>"""


def new_integration_request_email_message(integration_request:str, email: str, integration_explaination: str) -> str:
    return f"""New Integration Request:<br/>
<br/>
Integration Request: {integration_request}<br/>
Email: {email}<br/>
Integration Explaination: {integration_explaination}</br>"""


def new_plan_purchase_email_message(username: str, email: str, plan_name: str) -> str:
    return f"""New Plan Purchase Alert:<br/>
<br/>
Name: {username}<br/>
Email: {email}<br/>
Plan: {plan_name}<br/>"""