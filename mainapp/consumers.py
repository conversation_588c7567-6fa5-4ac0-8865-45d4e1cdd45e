import logging

from channels.generic.websocket import AsyncJsonWebsocketConsumer

from ColdEmailerBackend.settings import DEBUG

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


class CampaignsPageConsumer(AsyncJsonWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(args, kwargs)
        self.group_name = None
        self.user_id = None

    async def connect(self):
        self.user_id = self.scope['url_route']['kwargs']['user_id']
        self.group_name = f"campaigns_page_{self.user_id}"

        # Add user to group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()

        logger.debug(f"Websocket connection {self.group_name} established")

    async def campaign_status_update(self, event):
        try:
            logger.debug(event)
            await self.send_json({
                "event_name": "campaign_status_update",
                "event_data": event["data"],
            })

        except Exception as e:
            logger.error(e)

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )


class ContactListPageConsumer(AsyncJsonWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(args, kwargs)
        self.group_name = None
        self.user_id = None

    async def connect(self):
        self.user_id = self.scope['url_route']['kwargs']['user_id']
        self.group_name = f"contact_list_page_{self.user_id}"

        # Add user to group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()

        logger.debug(f"Websocket connection {self.group_name} established")

    async def contact_list_status_update(self, event):
        try:
            logger.debug(event)
            await self.send_json({
                "event_name": "contact_list_status_update",
                "event_data": event["data"],
            })

        except Exception as e:
            logger.error(e)

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
