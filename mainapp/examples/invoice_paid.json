{"api_version": "2025-04-30.basil", "created": **********, "data": {"object": {"account_country": "IN", "account_name": "<PERSON><PERSON> Dev Sandbox", "account_tax_ids": null, "amount_due": 283082, "amount_overpaid": 0, "amount_paid": 283082, "amount_remaining": 0, "amount_shipping": 0, "application": null, "attempt_count": 0, "attempted": true, "auto_advance": false, "automatic_tax": {"disabled_reason": null, "enabled": false, "liability": null, "provider": null, "status": null}, "automatically_finalizes_at": null, "billing_reason": "subscription_create", "collection_method": "charge_automatically", "created": **********, "currency": "inr", "custom_fields": null, "customer": "cus_SQlclSyXWR1aZG", "customer_address": {"city": "Mumbai", "country": "IN", "line1": "123 Apartments", "line2": "Fake Streets", "postal_code": "400703", "state": "MH"}, "customer_email": "<EMAIL>", "customer_name": "<PERSON>", "customer_phone": null, "customer_shipping": null, "customer_tax_exempt": "none", "customer_tax_ids": [], "default_payment_method": null, "default_source": null, "default_tax_rates": [], "description": null, "discounts": [], "due_date": null, "effective_at": **********, "ending_balance": 0, "footer": null, "from_invoice": null, "hosted_invoice_url": "https://invoice.stripe.com/i/acct_1RSESwSBdPcIAJFy/test_YWNjdF8xUlNFU3dTQmRQY0lBSkZ5LF9TUWxkR0tmOXNxbXdUS3B6OGYzbThORGJiNEY2cjlTLDEzOTQ5NTA1Mg0200PYBvYJ45?s=ap", "id": "in_1RVu5rSBdPcIAJFyiY8S3ZER", "invoice_pdf": "https://pay.stripe.com/invoice/acct_1RSESwSBdPcIAJFy/test_YWNjdF8xUlNFU3dTQmRQY0lBSkZ5LF9TUWxkR0tmOXNxbXdUS3B6OGYzbThORGJiNEY2cjlTLDEzOTQ5NTA1Mg0200PYBvYJ45/pdf?s=ap", "issuer": {"type": "self"}, "last_finalization_error": null, "latest_revision": null, "lines": {"data": [{"amount": 239900, "currency": "inr", "description": "1 × Basic (at ₹2,399.00 / month)", "discount_amounts": [], "discountable": true, "discounts": [], "id": "il_1RVu5qSBdPcIAJFynZ45ye1r", "invoice": "in_1RVu5rSBdPcIAJFyiY8S3ZER", "livemode": false, "metadata": {"workspace_id": "13"}, "object": "line_item", "parent": {"invoice_item_details": null, "subscription_item_details": {"invoice_item": null, "proration": false, "proration_details": {"credited_items": null}, "subscription": "sub_1RVu5rSBdPcIAJFyZVHPBKGD", "subscription_item": "si_SQldpTrn6rnD5y"}, "type": "subscription_item_details"}, "period": {"end": 1751546232, "start": **********}, "pretax_credit_amounts": [], "pricing": {"price_details": {"price": "price_1RUmb5SBdPcIAJFyeaq2y2ij", "product": "prod_SMzJK4cilGyIy0"}, "type": "price_details", "unit_amount_decimal": "239900"}, "quantity": 1, "taxes": [{"amount": 43182, "tax_behavior": "exclusive", "tax_rate_details": {"tax_rate": "txr_1RUmpESBdPcIAJFyMbyBNUg3"}, "taxability_reason": "not_available", "taxable_amount": 239900, "type": "tax_rate_details"}]}], "has_more": false, "object": "list", "total_count": 1, "url": "/v1/invoices/in_1RVu5rSBdPcIAJFyiY8S3ZER/lines"}, "livemode": false, "metadata": {}, "next_payment_attempt": null, "number": "CLVGOZ6D-0001", "object": "invoice", "on_behalf_of": null, "parent": {"quote_details": null, "subscription_details": {"metadata": {"workspace_id": "13"}, "subscription": "sub_1RVu5rSBdPcIAJFyZVHPBKGD"}, "type": "subscription_details"}, "payment_settings": {"default_mandate": null, "payment_method_options": {"acss_debit": null, "bancontact": null, "card": {"request_three_d_secure": "automatic"}, "customer_balance": null, "konbini": null, "sepa_debit": null, "us_bank_account": null}, "payment_method_types": null}, "period_end": **********, "period_start": **********, "post_payment_credit_notes_amount": 0, "pre_payment_credit_notes_amount": 0, "receipt_number": null, "rendering": null, "shipping_cost": null, "shipping_details": null, "starting_balance": 0, "statement_descriptor": null, "status": "paid", "status_transitions": {"finalized_at": **********, "marked_uncollectible_at": null, "paid_at": **********, "voided_at": null}, "subtotal": 239900, "subtotal_excluding_tax": 239900, "test_clock": null, "total": 283082, "total_discount_amounts": [], "total_excluding_tax": 239900, "total_pretax_credit_amounts": [], "total_taxes": [{"amount": 43182, "tax_behavior": "exclusive", "tax_rate_details": {"tax_rate": "txr_1RUmpESBdPcIAJFyMbyBNUg3"}, "taxability_reason": "not_available", "taxable_amount": 239900, "type": "tax_rate_details"}], "webhooks_delivered_at": **********}}, "id": "evt_1RVu5wSBdPcIAJFyCz4n9bx2", "livemode": false, "object": "event", "pending_webhooks": 1, "request": {"id": null, "idempotency_key": null}, "type": "invoice.paid"}