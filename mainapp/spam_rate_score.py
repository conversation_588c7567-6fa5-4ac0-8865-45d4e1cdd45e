import re
from typing import List

from pydantic import BaseModel

from mainapp.models import SpamWord, FriendlySalutation


class SpamRateScoreResult(BaseModel):
    score: int
    verdict: str
    suggestions: List[str]


class SpamRateScore:
    def __init__(self):
        self._spam_words = list(SpamWord.objects.all().values_list('name', flat=True))
        self._friendly_salutations = list(FriendlySalutation.objects.all().values_list('name', flat=True))

    def _count_spam_words(self, text: str) -> int:
        """

        :param text:
        :return:
        """
        regex = re.compile(r'\b(' + '|'.join(re.escape(word) for word in self._spam_words) + r')\b', re.IGNORECASE)
        return len(regex.findall(text))

    def _find_fuzzy_spam_matches(self, text: str) -> List[str]:
        """

        :param text:
        :return:
        """
        lowered = text.lower()
        detected = set()
        for word in self._spam_words:
            normalized = re.sub(r'[^a-z0-9]', '', word.lower())
            variants = [
                normalized.replace('e', '3'),
                normalized.replace('o', '0'),
                normalized.replace('a', '@'),
                normalized.replace('i', '1'),
            ]
            for variant in variants:
                if variant in lowered:
                    detected.add(variant)

        return list(detected)

    @staticmethod
    def _count_links(text: str) -> int:
        """

        :param text:
        :return:
        """
        return len(re.findall(r'https?://\S+', text))

    @staticmethod
    def _check_personalization(message: str) -> bool:
        """
        Check if first three lines of the email body contains any variables in the format {variable_name}.

        :param message:
        :return:
        """
        # Get the first three lines.
        text: str = " ".join([line for line in list(filter(lambda x: x.strip() != "", message.split('\n')))[:3]])

        # Regular expression to match {variable_name} pattern
        # This looks for any non-empty string between curly braces
        pattern = r'\{([^{}]+)\}'

        # Search for matches in the email body
        matches = re.search(pattern, text)

        # Return True if at least one match is found, False otherwise
        return matches is not None

    @staticmethod
    def _contains_too_many_emojis(text: str) -> bool:
        """

        :param text:
        :return:
        """
        pattern: str = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\u2600-\u26FF\u2700-\u27BF]'
        result = re.findall(pattern, text)
        return len(result) > 2

    @staticmethod
    def _contains_non_ascii(text: str) -> bool:
        return bool(re.search(r'[^\x00-\x7F]', text))

    def _has_friendly_salutation(self, text: str) -> bool:
        lowered = text.lower()
        return any(sal in lowered for sal in self._friendly_salutations)

    def _generate_suggestions(self, subject: str, content: str) -> List[str]:
        suggestions = []
        combined = subject + "\n" + content

        if not self._check_personalization(content):
            suggestions.append("Add personalization token (e.g., {name}) at the beginning of the email.")

        if not self._has_friendly_salutation(content):
            suggestions.append("End the email with a friendly closing like 'Thanks', 'Warm regards', or 'Cheers'.")

        if self._count_spam_words(combined) > 1:
            suggestions.append("Reduce usage of spam trigger words like 'free', 'guarantee', 'limited time'.")

        fuzzy_matches = list(set(self._find_fuzzy_spam_matches(combined)))
        if fuzzy_matches:
            suggestions.append(f"Avoid obfuscated spammy words like: {', '.join(fuzzy_matches)}")

        if self._count_links(content) > 1:
            suggestions.append("Reduce number of links, especially in short emails.")

        if len(subject) > 50:
            suggestions.append("Shorten the subject line for better deliverability.")

        if (re.match(r"^(re|fwd):", subject, re.IGNORECASE) and
                not re.search(r"conversation|follow", subject, re.IGNORECASE)):
            suggestions.append("Avoid misleading prefixes like 'Re:' or 'Fwd:' unless it's a reply.")

        if re.search(r"[€£¥₹$]", subject):
            suggestions.append("Avoid using currency symbols in subject lines.")

        big_numbers = re.findall(r"\d{4,}", subject)
        if big_numbers:
            non_year_numbers = [n for n in big_numbers if int(n) < 1970 or int(n) > 2030]
            if non_year_numbers:
                suggestions.append("Avoid large numbers in subject lines.")

        if subject.isupper() and len(subject) > 5:
            suggestions.append("Avoid writing subject in all caps.")

        if self._contains_too_many_emojis(subject):
            suggestions.append("Use fewer emojis in the subject line.")

        if re.search(r"!{3,}|\?{3,}", subject):
            suggestions.append("Avoid excessive punctuation like !!! or ??? in subject.")

        return suggestions

    def score_email(self, subject: str, content: str) -> SpamRateScoreResult:
        """
        Returns spam score in following dictionary:

        :param subject:
        :param content:
        :return:
        """
        subject = subject.strip()
        content = content.strip()

        if not subject:
            raise ValueError("No 'subject' value was provided.")

        if not content:
            raise ValueError("No 'content' value was provided.")

        word_count = len(content.strip().split())
        spam_count = self._count_spam_words(subject + " " + content)
        fuzzy_matches = self._find_fuzzy_spam_matches(subject + " " + content)
        link_count = self._count_links(content)
        has_personalization = self._check_personalization(content)
        has_salutation = self._has_friendly_salutation(content)

        score = 100

        if fuzzy_matches:
            score -= 40
        elif spam_count >= 2:
            score -= 40
        elif spam_count == 1:
            score -= 10

        if link_count == 1 and word_count < 50:
            score -= 12
        elif link_count == 1:
            score -= 3
        elif link_count >= 2 and word_count < 250:
            score -= 12
        elif link_count >= 2:
            score -= 6

        if not has_personalization:
            score -= 10
        if not has_salutation:
            score -= 10

        subject_penalty = 0
        lowered_subject = subject.lower()

        if any(w in lowered_subject for w in self._spam_words):
            subject_penalty += 5

        if subject.isupper() and len(subject) > 5:
            subject_penalty += 3

        if self._contains_too_many_emojis(subject):
            subject_penalty += 3

        if self._contains_non_ascii(subject):
            subject_penalty += 3

        if re.search(r"[€£¥₹$]", subject):
            subject_penalty += 3

        if re.search(r"!{3,}|\?{3,}", subject):
            subject_penalty += 3

        if len(subject) > 50:
            subject_penalty += 3

        if (re.match(r"^(re|fwd):", subject, re.IGNORECASE) and
                not re.search(r"conversation|follow", subject, re.IGNORECASE)):
            subject_penalty += 3

        big_numbers = re.findall(r"\d{4,}", subject)
        if big_numbers:
            non_year_numbers = [n for n in big_numbers if int(n) < 1970 or int(n) > 2030]
            if non_year_numbers:
                subject_penalty += 4

        score -= subject_penalty
        score = min(max(score, 0), 100)

        if score < 75:
            verdict = "❌ Spam Risk"
        elif score < 85:
            verdict = "⚠️ Promotions Risk"
        else:
            verdict = "✅ Inbox Likely"

        suggestions = self._generate_suggestions(subject, content)

        return SpamRateScoreResult(score=score, verdict=verdict, suggestions=suggestions)


if __name__ == '__main__':
    sub = "Let's Collaborate: Boosting Our Reach with Undetectable AI's 15M+ User Base"
    con = '''Hi Amin,

    I hope you are doing great. I wanted to reach out to you about a potential collaboration. At Undetectable AI, we're always looking to build valuable partnerships that benefit both parties. With a community of over 15 million users, we're eager to explore ways we can promote each other's content and products.

    I'd love to learn more about your current content goals and see how we can create a mutually beneficial partnership. If you're interested, let's chat!

    Looking forward to hearing from you.

    Kind regards,
    Shelly Lopez
    Outreach/Content Specialist
    Link Building/SEO Team | Undetectable AI'''
    srs = SpamRateScore()
    print(srs.score_email(sub, con))
