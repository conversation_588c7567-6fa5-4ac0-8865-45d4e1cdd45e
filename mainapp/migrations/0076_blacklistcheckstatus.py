# Generated by Django 5.1.2 on 2025-07-26 13:31

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0075_alter_campaignschedule_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlacklistCheckStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('checked_on', models.DateField(auto_now_add=True)),
                ('blacklisted_sources', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, size=None)),
                ('subdomain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.managedsubdomain')),
            ],
            options={
                'unique_together': {('subdomain', 'checked_on')},
            },
        ),
    ]
