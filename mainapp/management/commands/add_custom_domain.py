import time
from typing import List

from django.core.management import BaseCommand

from ColdEmailerBackend.settings import aws_ses
from mainapp.models import ManagedSubdomain
from mainapp.route53 import add_route53_record
from mainapp.tasks import email_tracking_setup_task


class Command(BaseCommand):
    """
    This script will add custom domain setup to one domain every run This needs to be run as a cronjob
    """
    def handle(self, *args, **options):
        domains = ManagedSubdomain.objects.filter(
            status="active",
            config_set_name=None,
            workspace__subscription_plan__is_free_plan=False
        )
        domain = domains[0]

        self.stdout.write(self.style.SUCCESS(f"[*] Fixing {domain.subdomain}..."))

        # Add tracking domain to SES Identity.
        tracking_link_domain = f"link.{domain.subdomain}"
        domain.tracking_link_domain = tracking_link_domain
        domain.save()

        try:
            response = aws_ses.create_email_identity(
                EmailIdentity=tracking_link_domain,
            )

            # Fetch and save the DKIM tokens.
            dkim_tokens: List[str] = response["DkimAttributes"]["Tokens"]
            domain.dkim_tokens = dkim_tokens
            domain.save()

            # Create CNAME records using DKIM tokens.
            for token in dkim_tokens:
                record_name = f"{token}._domainkey.{tracking_link_domain}"
                record_value = f"{token}.dkim.amazonses.com"
                try:
                    add_route53_record(
                        hosted_zone_id=domain.hosted_zone_id,
                        record_type="CNAME",
                        record_name=record_name,
                        record_value=record_value,
                    )
                except Exception as err:
                    self.stdout.write(self.style.ERROR(f"{err}"))

        except aws_ses.exceptions.AlreadyExistsException:
            self.stdout.write(self.style.WARNING(f"Identity {tracking_link_domain} has already been created on SES."))

        # Wait 10 seconds to make sure verification is done for link subdomain.
        time.sleep(10)

        email_tracking_setup_task.delay(domain.id)
        self.stdout.write(self.style.SUCCESS(f"[*] Celery task started for {domain.subdomain}"))
