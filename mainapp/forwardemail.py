import json
import logging
import os
from typing import Dict

import requests
from requests.auth import HTT<PERSON><PERSON>asic<PERSON>uth
from retry import retry

logger = logging.getLogger('coldemailer.forwardemail')


class FECreateDomainException(Exception):
    def __init__(self, domain: str, status_code: int, response_content: bytes):
        self.domain = domain
        self.status_code = status_code
        self.response = response_content
        super().__init__(f"Failed to add {self.domain} on forwardemail.net: "
                         f"Status Code {self.status_code}")


class FECreateAliasException(Exception):
    def __init__(self, domain: str, alias_address: str, status_code: int, response_content: bytes):
        self.domain = domain
        self.alias_address = alias_address
        self.status_code = status_code
        self.response = response_content
        super().__init__(f"Failed to create alias {self.alias_address} on forwardemail.net: "
                         f"Status Code {self.status_code}")


@retry(Exception, tries=5, delay=1, backoff=2)
def forwardemail_create_domain(domain: str) -> str:
    """
    Adds domain to forwardemail.net.

    :param domain: Email domain value (ex. mailer.hey.draftss.com)
    :returns: Verification record value.
    """
    res = requests.post(
        url="https://api.forwardemail.net/v1/domains",
        # Password needs to be empty.
        auth=HTTPBasicAuth(os.environ["CE_FORWARDEMAIL_AUTH_TOKEN"], ""),
        data={"domain": domain}
    )
    if res.status_code != 200:
        raise FECreateDomainException(
            domain=domain,
            status_code=res.status_code,
            response_content=res.content,
        )

    # Get the verification_record value for creating TXT record.
    res_data: Dict = json.loads(res.content)
    verification_record: str = res_data["verification_record"]

    return verification_record


@retry(Exception, tries=3, delay=1, backoff=2)
def forwardemail_verify_records(domain: str) -> bool:
    """
    Calls API to verify records in forwardemail.net.

    :param domain: Domain that was registerd with forwardemail.net.
    :returns: True if verfication was done/successful. False otherwise.
    """
    res = requests.get(
        url=f"https://api.forwardemail.net/v1/domains/{domain}/verify-records",
        # Password needs to be empty.
        auth=HTTPBasicAuth(os.environ["CE_FORWARDEMAIL_AUTH_TOKEN"], ""),
    )
    return res.status_code == 200


@retry(Exception, tries=5, delay=1, backoff=2)
def forwardemail_create_alias(domain: str, alias: str, destination_email: str):
    res = requests.post(
        url=f"https://api.forwardemail.net/v1/domains/{domain}/aliases",
        auth=HTTPBasicAuth(os.environ["CE_FORWARDEMAIL_AUTH_TOKEN"], ""),
        data={
            "name": alias,
            "recipients": [destination_email, os.environ["CE_FORWARDEMAIL_WEBHOOK"]],
            "has_recipient_verification": False,
            "is_enabled": True
        }
    )
    if res.status_code == 200:
        print(f"[*] Alias {alias}@{domain} created successfully!")

    else:
        raise FECreateAliasException(
            domain=domain,
            alias_address=alias,
            status_code=res.status_code,
            response_content=res.content,
        )


def forwardemail_delete_domain(email_subdomain: str):
    """
    Deletes domain from forwardemail.net.
    """
    res = requests.delete(
        url=f"https://api.forwardemail.net/v1/domains/{email_subdomain}",
        auth=HTTPBasicAuth(os.environ["CE_FORWARDEMAIL_AUTH_TOKEN"], ""),
    )
    if res.status_code == 200:
        logger.debug(f"Email subdomain {email_subdomain} removed from Forwardemail.net successfully.")

    else:
        logger.error(f"Failed to remove email subdomain {email_subdomain} from Forwardemail.net: "
                     f"Status Code: {res.status_code} | "
                     f"Response Content: {res.content}")
