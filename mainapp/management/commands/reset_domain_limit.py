from datetime import timedelta

from django.core.management import BaseCommand
from django.utils import timezone

from ColdEmailerBackend.settings import DOMAIN_LIMIT_RESET_DAYS, INITIAL_DAILY_EMAIL_LIMIT
from mainapp.models import ManagedSubdomain


class Command(BaseCommand):
    def handle(self, *args, **options):
        # Fetch all connected domains whose last email sent was DOMAIN_LIMIT_RESET_DAYS days ago.
        days_threshold = timezone.now() - timedelta(days=DOMAIN_LIMIT_RESET_DAYS)
        for msub in ManagedSubdomain.objects.filter(last_email_sent_on__lte=days_threshold):
            msub.email_limit = INITIAL_DAILY_EMAIL_LIMIT
            msub.last_email_sent_on = None  # to avoid this domain from being read again until updated.
            msub.save()
            self.stdout.write(f"Domain {msub.subdomain}'s limit reset back to {INITIAL_DAILY_EMAIL_LIMIT}")

        self.stdout.write("All Done!")
