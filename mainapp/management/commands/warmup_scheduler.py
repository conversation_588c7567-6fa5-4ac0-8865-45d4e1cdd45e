from django.core.management import BaseCommand

from mainapp.models import ManagedSubdomain
from mainapp.utils import create_warmup_schedules

"""
This should be run as a cronjob.
At 00:00 UTC, schedules warmup for every connected domain/subdomain.
"""


class Command(BaseCommand):
    def handle(self, *args, **options):
        for msub in ManagedSubdomain.objects.filter(setup_complete=True):
            self.stdout.write(f"[*] Creating schedules for {msub.subdomain}")
            create_warmup_schedules(msub)

        self.stdout.write("[*] All Done!")
