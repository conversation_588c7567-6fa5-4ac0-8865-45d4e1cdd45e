# Generated by Django 5.1.2 on 2025-01-04 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0010_campaign_reply_to_address'),
    ]

    operations = [
        migrations.CreateModel(
            name='BadEmail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_id', models.CharField(db_index=True, max_length=500, unique=True)),
                ('added_on', models.DateTimeField(auto_now_add=True)),
                ('reason', models.TextField(default=None, null=True)),
            ],
        ),
        migrations.AlterField(
            model_name='campaignschedule',
            name='status',
            field=models.CharField(choices=[('created', 'Created'), ('sent', 'Sent'), ('failed', 'Failed'), ('replied', 'Replied'), ('cancelled_reply_received', 'Cancelled - Reply Received'), ('cancelled_unsubscribed', 'Cancelled - Unsubscribed'), ('cancelled_bad_email', 'Cancelled - Bad Email'), ('cancelled_campaign_stopped', 'Cancelled - Campaign Stopped')], default='created', max_length=100),
        ),
    ]
