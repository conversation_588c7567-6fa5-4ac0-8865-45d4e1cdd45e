{"notificationType": "Received", "mail": {"timestamp": "2024-12-18T07:14:00.381Z", "source": "<EMAIL>", "messageId": "aq0hn48ekpqbbung91p8tge1s40avu5ee859h9g1", "destination": ["<EMAIL>"], "headersTruncated": false, "headers": [{"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Received", "value": "from sonic309-21.consmr.mail.sg3.yahoo.com (sonic309-21.consmr.mail.sg3.yahoo.com [*************]) by inbound-smtp.us-east-1.amazonaws.com with SMTP id aq0hn48ekpqbbung91p8tge1s40avu5ee859h9g1 for <EMAIL>; Wed, 18 Dec 2024 07:14:00 +0000 (UTC)"}, {"name": "X-SES-Spam-Verdict", "value": "PASS"}, {"name": "X-SES-Virus-Verdict", "value": "PASS"}, {"name": "Received-SPF", "value": "pass (spfCheck: domain of _spf.mail.yahoo.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=sonic309-21.consmr.mail.sg3.yahoo.com;"}, {"name": "Authentication-Results", "value": "amazonses.com; spf=pass (spfCheck: domain of _spf.mail.yahoo.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=sonic309-21.consmr.mail.sg3.yahoo.com; dkim=pass header.i=@yahoo.com; dmarc=pass header.from=yahoo.com;"}, {"name": "X-SES-RECEIPT", "value": "AEFBQUFBQUFBQUFHQk9uYldZRURBVStoQmRrR3NzOVRBTDRSRk56eTQ3dzhvL0FHb1B2d01DaXlqZVFLRmpGZlEyZFV5NUN3OWJ1NXZSTlVxQmxBdVkzUnlsZnB3bGVTVDh3QkRvTXUvaEc1UkZZMXRyR2J1eVV1MlRPTmFuN1BQSlluUllnSkUwcjZCYXc5bmp6UEVQbldSWTJ5MFhhc0hHeDQxajJlTVNLb1BVV0sxbXROaVEySmJLOTYvQml2d2FWUmZwSnR2RHRkaGI4MkhBd204OFNwZ0RvaExXYjUrTHpBZTMzektCVm81eXFIZ2V2T3dwblhOcm5HQWdJTDhpUSt2eDJrczJhbmhJUWZPaHRJQklSZzNxMytNV08rby8vUGRpYUtFOWlGUmljY0VwaHBmOHc9PQ=="}, {"name": "X-SES-DKIM-SIGNATURE", "value": "a=rsa-sha256; q=dns/txt; b=qpLtBhmbuQ+sUmvdqYaiGYSlY4BtAhCTcHo1gzEfplePsl4wAk1bupAsibBZtRV1qI8pK2eZLwxVdo/yCKBRfuBy2XhCSFbc/PcieXDrFoE4FWCwtC2mPzHVDVk92zxOpiUla4PEyWL58506/f9diC8o04gydzVe0Yw/E8XExZw=; c=relaxed/simple; s=ug7nbtf4gccmlpwj322ax3p6ow6yfsug; d=amazonses.com; t=1734506041; v=1; bh=M2FDAM3wbiAdcWOS99Www4hGyZm3PPuI68eipUwqWvA=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;"}, {"name": "DKIM-Signature", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed; d=yahoo.com; s=s2048; t=1734506037; bh=bEoYipdFyw7eLNwsbhLS5KSxGlCAYKWMevTzPKWy+WI=; h=Date:From:To:In-Reply-To:References:Subject:From:Subject:Reply-To; b=Df/Gyjb37a1JW0qx+SkxkW6rKUDlyAT6j8CvFY0OBUhWNP/CdcphCsxvYpae3EC6KAc9BqXDbnf8j1xNMR/gluo6UkHwrLhrjjwmSphnCZiOUSkjjK60rDAmIOg1hzOOdl32LJsvfm2M8gbpON2SBrHk+pR5ykMBcB9819wVO/Dc6RfB7fBJvMsa06RErxx9yCRcIbYWJh3J55mpG8lch0yA+m+ovyz/I1TygbqHKytYN/Fn1wDJ8CjLDSpPYrpDIOA6nnmIQIonPHvDZe03Vy0K89lN0WydvNT7qdcJy4McnNbg2a5WIOM+WYyt54hsN3BI3zlbUCubcAKaduy4xg=="}, {"name": "X-SONIC-DKIM-SIGN", "value": "v=1; a=rsa-sha256; c=relaxed/relaxed; d=yahoo.com; s=s2048; t=1734506037; bh=8HZSpKNFX186u926h8d9k1SN3H4IcaP67XcdJuCabeY=; h=X-Sonic-MF:Date:From:To:Subject:From:Subject; b=qINdIyQY/VJ1+QiZ/ofPfJGVGmwS+QYSuKB2Htiht3DD/I0Gb2yFuGRgPeioN0bfE9fMjUdhNxXg3ew2zBaR0NCGpQoTLLL+EcrmZzyqsexReLCfwUjW9bTOfGiITTLJq2WqxQbYUiZVZIdStRdIWkBiqcotW9uGOoUJCrL+s1J3oeCaaYZSjSVVEOo92Zwi0vu0qQjd5sR2tOn0iA6rrZoIZ4BmgJ31PsrUgUfchlWMfOYta2l3ZGjFN+YefC8H/D82rMYaU6dZTzmrr7fDugGOfUQmJLq7VGpRjPEGh6nZXg33oYTh8ExY9Pi5LjHg+T8SDTW18w9GaZdljud2zA=="}, {"name": "X-YMail-OSG", "value": "ah8KlMIVM1mLutOTN0fSA9xkM6QHz83qRqW_FOxFVrCsNXi4jXirAiLhnm8TcXO dSH0wLFYOiqb_WaFaupBTAOMMudmTSCpD20dMpQG3S1ySM__o_ignbiMDIxTmq1dRvT3Oz21fK8N QSeAX7ai7oHmZ2r1a_YclNfWmbPxdeHC3UhunwbmCow0DHo6E5bsL83r02z6s6N9SYMAv7VNEg1U oWNzxv8oNQVm17748NcyJhwDqf5C6oxeBf9I4XxW1EzZwS1hzIgUqXedH6NrQXGTslokykZMIWAf we0LtZ_FH0RDncOkPxzpC8fe_KxpVkANU7Z62gOsZ_qyTelynyy2sec3NWRchNmVPJL3J3CCkeUB Hyp2aVfnut2BRp5LxvY0n25hByoyf45_dIXTvXO45SYqNLbzPIHw3SMoIMQ_tjeHQGGoj3R4Vu66 _c9TMVSPuwszIpnfRPQDyQjQAqZfT46vBwHqbSXKLgX_NchZNc0Xw2s3vZSb5V9aFETH5fH9f6FM EDNgu1yVhpi2u7SQbQ.sirop1RHOQ1cYHbvisvnrfwoKV1Yo2YOSEfyOfMZcKYw4sEbeK3PeJcXY JI23HYcsRwLwOYWmUUGnYwDEW5luYn6vF.yFeF3ZuUDKtklI16Zfmt.JQFa09D9lsAHcY0pdFp2r 99DyiFnkutsJddZBbI.eQfiROKMMUcOTp1CAmOstPBkKDKh0W7TPgNvFp.J4wYcaErJ8ILCIlwwt sc6IOZhmNrFc8eCDPTizzSPIfljfTbytMewTcY8sQRtSBppR6c6wW9v4XCVLmoaZugB5aP1r156S 47WSl1hFfJF91X796Qfy_eIR8oSQLKToyztoCJL4mpB2Fv.PtyIwxgmUHtdIPrkQsXOx6fJgqAnP 77iCmTCX8HLzpeLta2PXJJ4B.EEo3g2pqjS6zb6lNR4whjWwdYW7e3dCF1FvbkQ1DsRfH2xWmzF3 IxczS55gWUfCKan7IMh2DjX0orSVqmY68ykXLDyDzGA5OS9CCqToftqCn3L7BQ5f2wKjWQI6Zm.l bZNmiGde.M59l3shlaMaskVpDPsSJD5QDsk5D0izRee51UHspWTa_nAOmAQxVveJPNQrAofO0cEO QIqZS.x0WJvOXXsNwFmbdwG_O42uVNEkW_Byo8VzA0ngz7Ivd224d73WR_7tJnz5TymmjAWS0IWy ZmvOwQmEcILDj4LcQvf8j1BNCVqcYbEICxgOvcwuR5b7p9yg3Z1gNCio_HR9PnYvPFpP9b6CWAoy b025GR9TvSq2DOa8wBiLrXqQ1UBE3zJ7Z9chK9WtGW29Gh0nvE83CsflwI4zFYv9LKPKZsorJ2ir vec6X_ktu3cV1wa3qRMy39D3iNNV58XQZRimL16TbBTAh9g5GF5ECSKiTVH1uDccClCfV_5x8p.. GwtsvaE3nWTtEkpNasy1zxOm9U2IIBYmrx3N8GgWdghWc_H7jFV1wAeVhyugDSXgH5775xtlWa26 Zz0dQgFSTnSpbx7GtK1Jz_q3Asaow3H9WzdynEBrd8b5K3TK0pfoYzDGnYyv7WFVJfkxTohyJDKE adOxMVZOibXdxhV1l31iSFOoJG.Xa1PBy1VDhZeNNGe1w2PGsXxX0YZHOKAMYxyO.9CDkey22EeU 98dLNFMAwKxf9PASOJhf_caSHjVjmaj5LMZ4tKscX8hyZQ4S3qLISjGyJB4V8RNFR6prv.rs5o9v Cb3OgLElptdBi2hB7s4wwByX1eaySy9ZwXPviL2tPrDVpI68sh7sBM0Uzc.uOa95jNGrpSv0cmck YuhJowQFKQv5iO9mG72ArelaBJ6wFVxWndeJMenNbb.29cZx8gZkJeq7oenSP3B744M.HlZKwUmz uGQ6xCxJNwSKVqlL4RO7KIt9CSzp5R5kuqpEGDVR7GUqoK83SsQw.g7oPAf8rdknF8UKd0k7pybp 7CzMiNZ7kzzEW.zDl30_JvKvnAojwrBpMCjcRQK8pe9_mM1KUBdzvQ5.nUZj4YTVeA7G6j6wmUHi 6Neix7NOyMCKYQskFQTbs3riN.gTgPIfYexS98YjmmC2gFsGuZzlcLsF5mhaRtvZUhXylC5ZvsgS gLPuGnDVBnlpchKtLiwK6fWsUAZkE82v7kD7neQEmUv1oc2yYDPzcf_lTYLKajMrKBhvnupW.hrF vOsN04QL46cwKYJC4VisAMGbep_.agLT2hakoEQn1VzRnotTa1kTWXQrpMVCuS5xqcMUVipLpElU dhxIk2.wm5psnzCrAdMAEM0OXGoSMPi_efwXgCbNazNWoyK.OTT4VJdpRVoUQYasC5881vmn1mup XCn111ZQdBegohRPLgJEG8DUNWG23pg--"}, {"name": "X-Sonic-MF", "value": "<<EMAIL>>"}, {"name": "X-Sonic-ID", "value": "bbf18fad-b451-41a3-bae4-e7104f72f70c"}, {"name": "Received", "value": "from sonic.gate.mail.ne1.yahoo.com by sonic309.consmr.mail.sg3.yahoo.com with HTTP; Wed, 18 Dec 2024 07:13:57 +0000"}, {"name": "Date", "value": "Wed, 18 Dec 2024 07:13:52 +0000 (UTC)"}, {"name": "From", "value": "j<PERSON><PERSON><PERSON><PERSON> nair <<EMAIL>>"}, {"name": "To", "value": "\"<EMAIL>\" <<EMAIL>>"}, {"name": "Message-ID", "value": "<<EMAIL>>"}, {"name": "In-Reply-To", "value": "<<EMAIL>>"}, {"name": "References", "value": "<<EMAIL>>"}, {"name": "Subject", "value": "Re: SES Email Receiving Test"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "Content-Type", "value": "multipart/alternative;  boundary=\"----=_Part_2850530_1392028428.1734506032584\""}, {"name": "X-Mailer", "value": "WebService/1.1.23040 YMailNorrin"}, {"name": "Content-Length", "value": "1375"}], "commonHeaders": {"returnPath": "<EMAIL>", "from": ["j<PERSON><PERSON><PERSON><PERSON> nair <<EMAIL>>"], "date": "Wed, 18 Dec 2024 07:13:52 +0000 (UTC)", "to": ["\"<EMAIL>\" <<EMAIL>>"], "messageId": "<<EMAIL>>", "subject": "Re: SES Email Receiving Test"}}, "receipt": {"timestamp": "2024-12-18T07:14:00.381Z", "processingTimeMillis": 1120, "recipients": ["<EMAIL>"], "spamVerdict": {"status": "PASS"}, "virusVerdict": {"status": "PASS"}, "spfVerdict": {"status": "PASS"}, "dkimVerdict": {"status": "PASS"}, "dmarcVerdict": {"status": "PASS"}, "action": {"type": "SNS", "topicArn": "arn:aws:sns:us-east-1:370841957185:coldemailer-reply-received", "encoding": "BASE64"}}, "content": "UmV0dXJuLVBhdGg6IDxhcHB1X2FydW5uYWlyQHlhaG9vLmNvbT4NClJlY2VpdmVkOiBmcm9tIHNvbmljMzA5LTIxLmNvbnNtci5tYWlsLnNnMy55YWhvby5jb20gKHNvbmljMzA5LTIxLmNvbnNtci5tYWlsLnNnMy55YWhvby5jb20gWzEwNi4xMC4yNDQuODRdKQ0KIGJ5IGluYm91bmQtc210cC51cy1lYXN0LTEuYW1hem9uYXdzLmNvbSB3aXRoIFNNVFAgaWQgYXEwaG40OGVrcHFiYnVuZzkxcDh0Z2UxczQwYXZ1NWVlODU5aDlnMQ0KIGZvciBpbmNvbWluZ0BkZXYuZGVsaXZlcnltYW4uYWk7DQogV2VkLCAxOCBEZWMgMjAyNCAwNzoxNDowMCArMDAwMCAoVVRDKQ0KWC1TRVMtU3BhbS1WZXJkaWN0OiBQQVNTDQpYLVNFUy1WaXJ1cy1WZXJkaWN0OiBQQVNTDQpSZWNlaXZlZC1TUEY6IHBhc3MgKHNwZkNoZWNrOiBkb21haW4gb2YgX3NwZi5tYWlsLnlhaG9vLmNvbSBkZXNpZ25hdGVzIDEwNi4xMC4yNDQuODQgYXMgcGVybWl0dGVkIHNlbmRlcikgY2xpZW50LWlwPTEwNi4xMC4yNDQuODQ7IGVudmVsb3BlLWZyb209YXBwdV9hcnVubmFpckB5YWhvby5jb207IGhlbG89c29uaWMzMDktMjEuY29uc21yLm1haWwuc2czLnlhaG9vLmNvbTsNCkF1dGhlbnRpY2F0aW9uLVJlc3VsdHM6IGFtYXpvbnNlcy5jb207DQogc3BmPXBhc3MgKHNwZkNoZWNrOiBkb21haW4gb2YgX3NwZi5tYWlsLnlhaG9vLmNvbSBkZXNpZ25hdGVzIDEwNi4xMC4yNDQuODQgYXMgcGVybWl0dGVkIHNlbmRlcikgY2xpZW50LWlwPTEwNi4xMC4yNDQuODQ7IGVudmVsb3BlLWZyb209YXBwdV9hcnVubmFpckB5YWhvby5jb207IGhlbG89c29uaWMzMDktMjEuY29uc21yLm1haWwuc2czLnlhaG9vLmNvbTsNCiBka2ltPXBhc3MgaGVhZGVyLmk9QHlhaG9vLmNvbTsNCiBkbWFyYz1wYXNzIGhlYWRlci5mcm9tPXlhaG9vLmNvbTsNClgtU0VTLVJFQ0VJUFQ6IEFFRkJRVUZCUVVGQlFVRkhRazl1WWxkWlJVUkJWU3RvUW1SclIzTnpPVlJCVERSU1JrNTZlVFEzZHpodkwwRkhiMUIyZDAxRGFYbHFaVkZMUm1wR1psRXlaRlY1TlVOM09XSjFOWFpTVGxWeFFteEJkVmt6VW5sc1puQjNiR1ZUVkRoM1FrUnZUWFV2YUVjMVVrWlpNWFJ5UjJKMWVWVjFNbFJQVG1GdU4xQlFTbGx1VWxsblNrVXdjalpDWVhjNWJtcDZVRVZRYmxkU1dUSjVNRmhoYzBoSGVEUXhhakpsVFZOTGIxQlZWMHN4YlhST2FWRXlTbUpMT1RZdlFtbDJkMkZXVW1ad1NuUjJSSFJrYUdJNE1raEJkMjA0T0ZOd1owUnZhRXhYWWpVclRIcEJaVE16ZWt0Q1ZtODFlWEZJWjJWMlQzZHdibGhPY201SFFXZEpURGhwVVN0MmVESnJjekpoYm1oSlVXWlBhSFJKUWtsU1p6TnhNeXROVjA4cmJ5OHZVR1JwWVV0Rk9XbEdVbWxqWTBWd2FIQm1PSGM5UFE9PQ0KWC1TRVMtREtJTS1TSUdOQVRVUkU6IGE9cnNhLXNoYTI1NjsgcT1kbnMvdHh0OyBiPXFwTHRCaG1idVErc1VtdmRxWWFpR1lTbFk0QnRBaENUY0hvMWd6RWZwbGVQc2w0d0FrMWJ1cEFzaWJCWnRSVjFxSThwSzJlWkx3eFZkby95Q0tCUmZ1QnkyWGhDU0ZiYy9QY2llWERyRm9FNEZXQ3d0QzJtUHpIVkRWazkyenhPcGlVbGE0UEV5V0w1ODUwNi9mOWRpQzhvMDRneWR6VmUwWXcvRThYRXhadz07IGM9cmVsYXhlZC9zaW1wbGU7IHM9dWc3bmJ0ZjRnY2NtbHB3ajMyMmF4M3A2b3c2eWZzdWc7IGQ9YW1hem9uc2VzLmNvbTsgdD0xNzM0NTA2MDQxOyB2PTE7IGJoPU0yRkRBTTN3YmlBZGNXT1M5OVd3dzRoR3labTNQUHVJNjhlaXBVd3FXdkE9OyBoPUZyb206VG86Q2M6QmNjOlN1YmplY3Q6RGF0ZTpNZXNzYWdlLUlEOk1JTUUtVmVyc2lvbjpDb250ZW50LVR5cGU6WC1TRVMtUkVDRUlQVDsNCkRLSU0tU2lnbmF0dXJlOiB2PTE7IGE9cnNhLXNoYTI1NjsgYz1yZWxheGVkL3JlbGF4ZWQ7IGQ9eWFob28uY29tOyBzPXMyMDQ4OyB0PTE3MzQ1MDYwMzc7IGJoPWJFb1lpcGRGeXc3ZUxOd3NiaExTNUtTeEdsQ0FZS1dNZXZUelBLV3krV0k9OyBoPURhdGU6RnJvbTpUbzpJbi1SZXBseS1UbzpSZWZlcmVuY2VzOlN1YmplY3Q6RnJvbTpTdWJqZWN0OlJlcGx5LVRvOyBiPURmL0d5amIzN2ExSlcwcXgrU2t4a1c2cktVRGx5QVQ2ajhDdkZZME9CVWhXTlAvQ2RjcGhDc3h2WXBhZTNFQzZLQWM5QnFYRGJuZjhqMXhOTVIvZ2x1bzZVa0h3ckxocmpqd21TcGhuQ1ppT1VTa2pqSzYwckRBbUlPZzFoek9PZGwzMkxKc3ZmbTJNOGdicE9OMlNCckhrK3BSNXlrTUJjQjk4MTl3Vk8vRGM2UmZCN2ZCSnZNc2EwNlJFcnh4OXlDUmNJYllXSmgzSjU1bXBHOGxjaDB5QSttK292eXovSTFUeWdicUhLeXRZTi9GbjF3REo4Q2pMRFNwUFlycERJT0E2bm5tSVFJb25QSHZEWmUwM1Z5MEs4OWxOMFd5ZHZOVDdxZGNKeTRNY25OYmcyYTVXSU9NK1dZeXQ1NGhzTjNCSTN6bGJVQ3ViY0FLYWR1eTR4Zz09DQpYLVNPTklDLURLSU0tU0lHTjogdj0xOyBhPXJzYS1zaGEyNTY7IGM9cmVsYXhlZC9yZWxheGVkOyBkPXlhaG9vLmNvbTsgcz1zMjA0ODsgdD0xNzM0NTA2MDM3OyBiaD04SFpTcEtORlgxODZ1OTI2aDhkOWsxU04zSDRJY2FQNjdYY2RKdUNhYmVZPTsgaD1YLVNvbmljLU1GOkRhdGU6RnJvbTpUbzpTdWJqZWN0OkZyb206U3ViamVjdDsgYj1xSU5kSXlRWS9WSjErUWlaL29mUGZKR1ZHbXdTK1FZU3VLQjJIdGlodDNERC9JMEdiMnlGdUdSZ1BlaW9OMGJmRTlmTWpVZGhOeFhnM2V3MnpCYVIwTkNHcFFvVExMTCtFY3JtWnp5cXNleFJlTENmd1VqVzliVE9mR2lJVFRMSnEyV3F4UWJZVWlaVlpJZFN0UmRJV2tCaXFjb3RXOXVHT29VSkNyTCtzMUozb2VDYWFZWlNqU1ZWRU9vOTJad2kwdnUwcVFqZDVzUjJ0T24waUE2cnJab0laNEJtZ0ozMVBzclVnVWZjaGxXTWZPWXRhMmwzWkdqRk4rWWVmQzhIL0Q4MnJNWWFVNmRaVHptcnI3ZkR1Z0dPZlVRbUpMcTdWR3BSalBFR2g2blpYZzMzb1lUaDhFeFk5UGk1TGpIZytUOFNEVFcxOHc5R2FaZGxqdWQyekE9PQ0KWC1ZTWFpbC1PU0c6IGFoOEtsTUlWTTFtTHV0T1ROMGZTQTl4a002UUh6ODNxUnFXX0ZPeEZWckNzTlhpNGpYaXJBaUxobm04VGNYTw0KIGRTSDB3TEZZT2lxYl9XYUZhdXBCVEFPTU11ZG1UU0NwRDIwZE1wUUczUzF5U01fX29faWduYmlNREl4VG1xMWRSdlQzT3oyMWZLOE4NCiBRU2VBWDdhaTdvSG1aMnIxYV9ZY2xOZldtYlB4ZGVIQzNVaHVud2JtQ293MERIbzZFNWJzTDgzcjAyejZzNk45U1lNQXY3Vk5FZzFVDQogb1dOenh2OG9OUVZtMTc3NDhOY3lKaHdEcWY1QzZveGVCZjlJNFh4VzFFelp3UzFoeklnVXFYZWRINk5yUVhHVHNsb2t5a1pNSVdBZg0KIHdlMEx0Wl9GSDBSRG5jT2tQeHpwQzhmZV9LeHBWa0FOVTdaNjJnT3NaX3F5VGVseW55eTJzZWMzTldSY2hObVZQSkwzSjNDQ2tlVUINCiBIeXAyYVZmbnV0MkJScDVMeHZZMG4yNWhCeW95ZjQ1X2RJWFR2WE80NVNZcU5MYnpQSUh3M1NNb0lNUV90amVIUUdHb2ozUjRWdTY2DQogX2M5VE1WU1B1d3N6SXBuZlJQUUR5UWpRQXFaZlQ0NnZCd0hxYlNYS0xnWF9OY2haTmMwWHcyczN2WlNiNVY5YUZFVEg1Zkg5ZjZGTQ0KIEVETmd1MXlWaHBpMnU3U1FiUS5zaXJvcDFSSE9RMWNZSGJ2aXN2bnJmd29LVjFZbzJZT1NFZnlPZk1aY0tZdzRzRWJlSzNQZUpjWFkNCiBKSTIzSFljc1J3THdPWVdtVVVHbll3REVXNWx1WW42dkYueUZlRjNadVVES3RrbEkxNlpmbXQuSlFGYTA5RDlsc0FIY1kwcGRGcDJyDQogOTlEeWlGbmt1dHNKZGRaQmJJLmVRZmlST0tNTVVjT1RwMUNBbU9zdFBCa0tES2gwVzdUUGdOdkZwLko0d1ljYUVySjhJTENJbHd3dA0KIHNjNklPWmhtTnJGYzhlQ0RQVGl6elNQSWZsamZUYnl0TWV3VGNZOHNRUnRTQnBwUjZjNndXOXY0WENWTG1vYVp1Z0I1YVAxcjE1NlMNCiA0N1dTbDFoRmZKRjkxWDc5NlFmeV9lSVI4b1NRTEtUb3l6dG9DSkw0bXBCMkZ2LlB0eUl3eGdtVUh0ZElQcmtRc1hPeDZmSmdxQW5QDQogNzdpQ21UQ1g4SEx6cGVMdGEyUFhKSjRCLkVFbzNnMnBxalM2emI2bE5SNHdoald3ZFlXN2UzZENGMUZ2YmtRMURzUmZIMnhXbXpGMw0KIEl4Y3pTNTVnV1VmQ0thbjdJTWgyRGpYMG9yU1ZxbVk2OHlrWExEeUR6R0E1T1M5Q0NxVG9mdHFDbjNMN0JRNWYyd0tqV1FJNlptLmwNCiBiWk5taUdkZS5NNTlsM3NobGFNYXNrVnBEUHNTSkQ1UURzazVEMGl6UmVlNTFVSHNwV1RhX25BT21BUXhWdmVKUE5RckFvZk8wY0VPDQogUUlxWlMueDBXSnZPWFhzTndGbWJkd0dfTzQydVZORWtXX0J5bzhWekEwbmd6N0l2ZDIyNGQ3M1dSXzd0Sm56NVR5bW1qQVdTMElXeQ0KIFptdk93UW1FY0lMRGo0TGNRdmY4ajFCTkNWcWNZYkVJQ3hnT3Zjd3VSNWI3cDl5ZzNaMWdOQ2lvX0hSOVBuWXZQRnBQOWI2Q1dBb3kNCiBiMDI1R1I5VHZTcTJET2E4d0JpTHJYcVExVUJFM3pKN1o5Y2hLOVd0R1cyOUdoMG52RTgzQ3NmbHdJNHpGWXY5TEtQS1pzb3JKMmlyDQogdmVjNlhfa3R1M2NWMXdhM3FSTXkzOUQzaU5OVjU4WFFaUmltTDE2VGJCVEFoOWc1R0Y1RUNTS2lUVkgxdURjY0NsQ2ZWXzV4OHAuLg0KIEd3dHN2YUUzbldUdEVrcE5hc3kxenhPbTlVMklJQlltcngzTjhHZ1dkZ2hXY19IN2pGVjF3QWVWaHl1Z0RTWGdINTc3NXh0bFdhMjYNCiBaejBkUWdGU1RuU3BieDdHdEsxSnpfcTNBc2FvdzNIOVd6ZHluRUJyZDhiNUszVEswcGZvWXpER25ZeXY3V0ZWSmZreFRvaHlKREtFDQogYWRPeE1WWk9pYlhkeGhWMWwzMWlTRk9vSkcuWGExUEJ5MVZEaFplTk5HZTF3MlBHc1h4WDBZWkhPS0FNWXh5Ty45Q0RrZXkyMkVlVQ0KIDk4ZExORk1Bd0t4ZjlQQVNPSmhmX2NhU0hqVmptYWo1TE1aNHRLc2NYOGh5WlE0UzNxTElTakd5SkI0VjhSTkZSNnBydi5yczVvOXYNCiBDYjNPZ0xFbHB0ZEJpMmhCN3M0d3dCeVgxZWF5U3k5WndYUHZpTDJ0UHJEVnBJNjhzaDdzQk0wVXpjLnVPYTk1ak5HcnBTdjBjbWNrDQogWXVoSm93UUZLUXY1aU85bUc3MkFyZWxhQko2d0ZWeFduZGVKTWVuTmJiLjI5Y1p4OGdaa0plcTdvZW5TUDNCNzQ0TS5IbFpLd1Vteg0KIHVHUTZ4Q3hKTndTS1ZxbEw0Uk83S0l0OUNTenA1UjVrdXFwRUdEVlI3R1Vxb0s4M1NzUXcuZzdvUEFmOHJka25GOFVLZDBrN3B5YnANCiA3Q3pNaU5aN2t6ekVXLnpEbDMwX0p2S3ZuQW9qd3JCcE1DamNSUUs4cGU5X21NMUtVQmR6dlE1Lm5VWmo0WVRWZUE3RzZqNndtVUhpDQogNk5laXg3Tk95TUNLWVFza0ZRVGJzM3JpTi5nVGdQSWZZZXhTOThZam1tQzJnRnNHdVp6bGNMc0Y1bWhhUnR2WlVoWHlsQzVadnNnUw0KIGdMUHVHbkRWQm5scGNoS3RMaXdLNmZXc1VBWmtFODJ2N2tEN25lUUVtVXYxb2MyeVlEUHpjZl9sVFlMS2FqTXJLQmh2bnVwVy5ockYNCiB2T3NOMDRRTDQ2Y3dLWUpDNFZpc0FNR2JlcF8uYWdMVDJoYWtvRVFuMVZ6Um5vdFRhMWtUV1hRcnBNVkN1UzV4cWNNVVZpcExwRWxVDQogZGh4SWsyLndtNXBzbnpDckFkTUFFTTBPWEdvU01QaV9lZndYZ0NiTmF6TldveUsuT1RUNFZKZHBSVm9VUVlhc0M1ODgxdm1uMW11cA0KIFhDbjExMVpRZEJlZ29oUlBMZ0pFRzhEVU5XRzIzcGctLQ0KWC1Tb25pYy1NRjogPGFwcHVfYXJ1bm5haXJAeWFob28uY29tPg0KWC1Tb25pYy1JRDogYmJmMThmYWQtYjQ1MS00MWEzLWJhZTQtZTcxMDRmNzJmNzBjDQpSZWNlaXZlZDogZnJvbSBzb25pYy5nYXRlLm1haWwubmUxLnlhaG9vLmNvbSBieSBzb25pYzMwOS5jb25zbXIubWFpbC5zZzMueWFob28uY29tIHdpdGggSFRUUDsgV2VkLCAxOCBEZWMgMjAyNCAwNzoxMzo1NyArMDAwMA0KRGF0ZTogV2VkLCAxOCBEZWMgMjAyNCAwNzoxMzo1MiArMDAwMCAoVVRDKQ0KRnJvbTogamFuYXJkaGFuYW4gbmFpciA8YXBwdV9hcnVubmFpckB5YWhvby5jb20+DQpUbzogImluY29taW5nQGRldi5kZWxpdmVyeW1hbi5haSIgPGluY29taW5nQGRldi5kZWxpdmVyeW1hbi5haT4NCk1lc3NhZ2UtSUQ6IDwyMDI2NDgzOTI1LjI4NTA1MzEuMTczNDUwNjAzMjU4NUBtYWlsLnlhaG9vLmNvbT4NCkluLVJlcGx5LVRvOiA8MDEwMDAxOTNkODlhMWRlYy01NDE5NThhNC04ZjVlLTQ5M2QtYTkwYi02NzA3Y2ZmYWY2MmUtMDAwMDAwQGVtYWlsLmFtYXpvbnNlcy5jb20+DQpSZWZlcmVuY2VzOiA8MDEwMDAxOTNkODlhMWRlYy01NDE5NThhNC04ZjVlLTQ5M2QtYTkwYi02NzA3Y2ZmYWY2MmUtMDAwMDAwQGVtYWlsLmFtYXpvbnNlcy5jb20+DQpTdWJqZWN0OiBSZTogU0VTIEVtYWlsIFJlY2VpdmluZyBUZXN0DQpNSU1FLVZlcnNpb246IDEuMA0KQ29udGVudC1UeXBlOiBtdWx0aXBhcnQvYWx0ZXJuYXRpdmU7IA0KCWJvdW5kYXJ5PSItLS0tPV9QYXJ0XzI4NTA1MzBfMTM5MjAyODQyOC4xNzM0NTA2MDMyNTg0Ig0KWC1NYWlsZXI6IFdlYlNlcnZpY2UvMS4xLjIzMDQwIFlNYWlsTm9ycmluDQpDb250ZW50LUxlbmd0aDogMTM3NQ0KDQotLS0tLS09X1BhcnRfMjg1MDUzMF8xMzkyMDI4NDI4LjE3MzQ1MDYwMzI1ODQNCkNvbnRlbnQtVHlwZTogdGV4dC9wbGFpbjsgY2hhcnNldD1VVEYtOA0KQ29udGVudC1UcmFuc2Zlci1FbmNvZGluZzogN2JpdA0KDQogaGVsbG8gYmFnc3phcA0KDQogICAgT24gV2VkbmVzZGF5LCAxOCBEZWNlbWJlciAyMDI0IGF0IDEyOjQwOjAwIHBtIElTVCwgaGVsbG9AZGV2LmJhZ3N6YXAuY29tIDxoZWxsb0BkZXYuYmFnc3phcC5jb20+IHdyb3RlOiAgDQogDQogUGxlYXNlIHJlcGx5IHRvIHRoaXMgZW1haWwgZm9yIHRlc3Rpbmcgc2VzIGVtYWlsIHJlY2VpdmluZy4NCiAgDQotLS0tLS09X1BhcnRfMjg1MDUzMF8xMzkyMDI4NDI4LjE3MzQ1MDYwMzI1ODQNCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sOyBjaGFyc2V0PVVURi04DQpDb250ZW50LVRyYW5zZmVyLUVuY29kaW5nOiA3Yml0DQoNCjxodG1sPjxoZWFkPjwvaGVhZD48Ym9keT48ZGl2IGNsYXNzPSJ5ZHA1ZTg1ODNhZnlhaG9vLXN0eWxlLXdyYXAiIHN0eWxlPSJmb250LWZhbWlseTpib29rbWFuIG9sZCBzdHlsZSwgbmV3IHlvcmssIHRpbWVzLCBzZXJpZjtmb250LXNpemU6MTNweDsiPjxkaXY+PC9kaXY+DQogICAgICAgIDxkaXYgZGlyPSJsdHIiIGRhdGEtc2V0ZGlyPSJmYWxzZSI+aGVsbG8gYmFnc3phcDxicj48L2Rpdj48ZGl2Pjxicj48L2Rpdj4NCiAgICAgICAgDQogICAgICAgIDwvZGl2PjxkaXYgaWQ9InlhaG9vX3F1b3RlZF80NjI0NTk1MjkxIiBjbGFzcz0ieWFob29fcXVvdGVkIj4NCiAgICAgICAgICAgIDxkaXYgc3R5bGU9ImZvbnQtZmFtaWx5OidIZWx2ZXRpY2EgTmV1ZScsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7Zm9udC1zaXplOjEzcHg7Y29sb3I6IzI2MjgyYTsiPg0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIDxkaXY+DQogICAgICAgICAgICAgICAgICAgICAgICBPbiBXZWRuZXNkYXksIDE4IERlY2VtYmVyIDIwMjQgYXQgMTI6NDA6MDAgcG0gSVNULCBoZWxsb0BkZXYuYmFnc3phcC5jb20gJmx0O2hlbGxvQGRldi5iYWdzemFwLmNvbSZndDsgd3JvdGU6DQogICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8ZGl2Pjxicj48L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgPGRpdj48YnI+PC9kaXY+DQogICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgPGRpdj48ZGl2IGRpcj0ibHRyIj5QbGVhc2UgcmVwbHkgdG8gdGhpcyBlbWFpbCBmb3IgdGVzdGluZyBzZXMgZW1haWwgcmVjZWl2aW5nLjxicj48L2Rpdj48L2Rpdj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj48L2JvZHk+PC9odG1sPg0KLS0tLS0tPV9QYXJ0XzI4NTA1MzBfMTM5MjAyODQyOC4xNzM0NTA2MDMyNTg0LS0NCg=="}