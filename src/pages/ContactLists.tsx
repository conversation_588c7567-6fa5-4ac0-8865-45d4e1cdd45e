import {
	Box,
	<PERSON>ton,
	Chip,
	Dialog,
	DialogActions,
	DialogContent,
	DialogContentText,
	DialogTitle,
	FormControl,
	InputLabel,
	Menu,
	MenuItem,
	Select,
	Stack,
	TextField,
	Typography,
	useTheme,
	FormGroup,
	FormControlLabel,
	Checkbox,
} from "@mui/material";
import React, {ChangeEvent, useEffect, useRef, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {CustomCellRendererProps} from "ag-grid-react";
import {CloudUpload, Delete, Downloading, MoreVert, Add} from "@mui/icons-material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {styled} from "@mui/material/styles";
import <PERSON> from "papaparse";
import {useSnackbar} from "notistack";
import {Link, useSearchParams} from "react-router-dom";
import {urls} from "@routes";
import {formatInTimeZone} from "date-fns-tz";
import {useDialogs} from "@toolpad/core";
import IconButton from "@mui/material/IconButton";

// "deleting" status is only present in frontend.
type ContactListStatus = "uploading" | "active" | "failed" | "deleting";

interface ContactList {
	uid: string
	name: string
	created_on: number
	status: ContactListStatus
	contacts_count: number
	column_count: number
	source: string
	available_columns: string[]
}

interface PageData {
	status_code: string
	status_text: string

	user_id: number
	contact_lists: ContactList[]
	timezone: string
}

interface WebhookMessage {
	event_type: string
	event_data: any
}


export default function ContactLists() {
	const theme = useTheme();
	const ws = useRef<WebSocket | null>(null);
	const [searchParams] = useSearchParams();
	const dialogs = useDialogs();
	const {enqueueSnackbar} = useSnackbar();

	const [userId, setUserId] = useState<number | null>(null);
	const [contactLists, setContactLists] = useState<ContactList[]>([]);
	const [checkContactListId, setCheckContactListId] = useState("")
	const [openColumnSelectionDialog, setOpenColumnSelectionDialog] = useState(false)
	const [availableColumns, setAvailableColumns] = useState<string[]>([])
	const [taskId, setTaskId] = useState("")
	const [openCsvImportDialog, setOpenCsvImportDialog] = useState<boolean>(
		searchParams.get("newimport") === "1"  // We'll open import dialog on page load if url param is set.
	);
	const [timezone, setTimezone] = useState("")

	// Page Title
	useEffect(() => {
		document.title = "Contact Lists - Deliveryman.ai";
	}, []);

	// Fetch the page data.
	const pageDataQuery = useQuery({
		queryKey: ["emailAccounts"],
		queryFn: () => authenticateAndFetchData("/contact-lists/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});

	const CheckTasKProgressMutation = useMutation({
		mutationKey: ["CheckTasKProgressMutation"],
		mutationFn: () => authenticateAndPostData("/get-celery-task-progress/", {
			task_id: taskId,
			contact_id: checkContactListId
		}),
		gcTime: 0,
		retry: retryFn,		
	})

	useEffect(() => {
		if (taskId) {
			pollTaskProgress();
		}
	}, [taskId]);

	useEffect(() => {
		if (pageDataQuery.data) {
			let data = pageDataQuery.data.data as PageData
			setContactLists(data.contact_lists);
			setUserId(data.user_id);
			setTimezone(data.timezone)
		}
	}, [pageDataQuery.data]);

	// Mutation for deleting contact list.
	const deleteContactListMutation = useMutation({
		mutationKey: ["deleteContactListMutation"],
		mutationFn: (contact_list_uid: string) => authenticateAndPostData("/delete-contact-list/", {
			contact_list_uid: contact_list_uid,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar("Contact list deleted successfully.", {
				variant: "success",
			});
			pageDataQuery.refetch().then();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	})

	// Websocket code
	useEffect(() => {
		const wsUrl = `${process.env.REACT_APP_WS_HOST}/ws/contact-list/${userId}/`;

		// Initialize WebSocket connection
		if (userId) {
			ws.current = new WebSocket(wsUrl);

			ws.current.onopen = () => {
				console.log("WebSocket connection established");
			};

			ws.current.onmessage = (event) => {
				const data: WebhookMessage = JSON.parse(event.data)["event_data"];
				const eventName: string = data["event_name"];
				const eventData: any = data["event_data"];

				if (eventName === "contact_list_status_update") {
					const contactListUID: string = eventData["contact_list_uid"];
					const newStatus: ContactListStatus = eventData["new_status"];
					const totalContacts: number = eventData["total_contacts"];
					const totalColumns: number = eventData["total_columns"];

					setContactLists(prevItems => prevItems.map(contactList => {
						return contactList.uid === contactListUID ? {
							...contactList,
							status: newStatus,
							contacts_count: totalContacts,
							column_count: totalColumns
						} : contactList
					}));
				} else {
					console.log("unhandled ws event:", event);
				}
			}

			ws.current.onclose = () => {
				console.log("WebSocket connection closed");
			};

			ws.current.onerror = (error) => {
				console.error("WebSocket error:", error);
			};
		}
	}, [userId]);

	const pollTaskProgress = () => {		
		const interval = setInterval(() => {
			CheckTasKProgressMutation.mutate(undefined, {
			onSuccess: (res) => {				
				const status = res.data.success;
				if (!status) {
					if (res.data.status == "pending"){
						setOpenColumnSelectionDialog(true)
						setAvailableColumns(res.data.available_columns)
					}
					clearInterval(interval);					
					setTaskId("")					
				}
			},
			onError: (err) => {
				console.error("Error fetching task progress:", err);
				clearInterval(interval);
				setTaskId("")	
			},
			});
		}, 2000);
	};


	function timestampFormatter(params: ValueFormatterParams, timezone: string) {
		if (!params.value) return "";

		return formatInTimeZone(
			new Date(params.value),
			timezone,
			"hh:mm a, do MMM yyyy"
		).replace(/\b(AM|PM)\b/, (match) => match.toLowerCase());
	}

	function closeCsvImportDialog() {
		setOpenCsvImportDialog(false);
	}

	/**
	 * Call this function when csv file import API call has completed successfully.
	 */
	function csvContactListImportSuccessful() {
		pageDataQuery.refetch().then();
		setOpenCsvImportDialog(false);
	}

	function closeOpenColumnSelection() {
		setOpenColumnSelectionDialog(false);
	}

	function ColumnSelectionSuccessful(){
		pageDataQuery.refetch().then();
		setOpenColumnSelectionDialog(false)
	}
	// --------------------- TABLE SETUP ---------------------

	const ContactListDetailsLink = (customProps: CustomCellRendererProps) => {
		return (
			<Link to={urls["contactListDetails"].replace(":contactListUID", customProps.data["uid"])}
						style={{color: theme.palette.text.primary}}>
				{customProps.data["name"]}
			</Link>
		);
	}

	const StatusBadgeCell = (customProps: CustomCellRendererProps) => {
		if (customProps.value === "uploading") {
			return <Chip size={"small"} color={"default"} label={"Uploading..."}/>

		} else if (customProps.value === "active") {
			return <Chip size={"small"} color={"success"} label={"Active"}/>

		} else if (customProps.value === "failed") {
			return <Chip size={"small"} color={"error"} label={"Failed"}/>

		} else if (customProps.value === "deleting") {
			return <Chip size={"small"} color={"error"} label={"Deleting..."}/>

		} else if (customProps.value === "pending") {
			return <Chip size={"small"} color={"warning"} label={"Pending"}/>
		} else {
			return <Chip size={"small"} color={"secondary"} label={"N/A"}/>
		}
	}

	const ImportCsvContactListButton = () => {
		return (
			<Button startIcon={<Downloading/>}
							variant="contained"
							color="primary"
							onClick={() => {
								setOpenCsvImportDialog(true);
							}}>
				Import
			</Button>
		)
	}

	const ActionsButton = (props: CustomCellRendererProps) => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
		const open = Boolean(anchorEl);

		const handleClick = (event: any) => {
			setAnchorEl(event.currentTarget);
		};

		const handleClose = () => {
			setAnchorEl(null);
		};

		const handleDelete = async () => {
			const cancelConfirmed = await dialogs.confirm(
				`Are you sure you want to delete this contact list? This won't affect any running campaigns, but you will 
				lose all stats generated in this list.`,
				{
					title: "Delete Contact List",
					cancelText: "Cancel",
					okText: "Confirm",
					severity: "error",
				}
			);
			if (cancelConfirmed) {
				setContactLists(prevState => {
					return prevState.map(value => {
						if (value.uid === props.data["uid"]) {
							value.status = "deleting";
							return value;
						} else {
							return value;
						}
					});
				});
				deleteContactListMutation.mutate(props.data["uid"]);
			}
			handleClose();
		};

		const  handleSelectedColumn = async () => {
			setCheckContactListId(props.data["uid"])
			setAvailableColumns(props.data["available_columns"])
			setOpenColumnSelectionDialog(true)

			handleClose();
		}

		return (
			<>
				<IconButton
					aria-label="more"
					aria-controls={open ? 'long-menu' : undefined}
					aria-expanded={open ? 'true' : undefined}
					aria-haspopup="true"
					onClick={handleClick}
					disabled={deleteContactListMutation.isPending}
				>
					<MoreVert/>
				</IconButton>
				<Menu
					id="long-menu"
					MenuListProps={{
						'aria-labelledby': 'long-button',
					}}
					anchorEl={anchorEl}
					open={open}
					onClose={handleClose}
				>
					<MenuItem onClick={handleDelete} disabled={deleteContactListMutation.isPending}>
						<Delete color={"error"} sx={{mr: 1}}/>
						<Typography variant="subtitle2">Delete Contact List</Typography>
					</MenuItem>
					{props.data["status"] === "pending" &&
						<MenuItem onClick={handleSelectedColumn}>
							<Add color={"primary"} sx={{mr: 1}}/>
							<Typography variant="subtitle2">Select Columns</Typography>
						</MenuItem>
					}
				</Menu>
			</>
		);
	}

	// Columns for contact lists table.
	const columnDefs: ColDef[] = [
		{field: "name", headerName: "Name", cellRenderer: ContactListDetailsLink},
		{
			field: "created_on",
			headerName: "Imported On",
			valueFormatter: (params) => timestampFormatter(params, timezone),
			flex: 2
		},
		{field: "contacts_count", headerName: "Total Contacts"},
		{field: "column_count", headerName: "Total Columns"},
		{field: "status", headerName: "Status", cellRenderer: StatusBadgeCell, sortable: false},
		{field: "source", headerName: "Source"},		
		{headerName: "Actions", cellRenderer: ActionsButton, sortable: false, resizable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading || pageDataQuery.isRefetching) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"column"} spacing={1}>
					<Typography variant={"h4"} fontWeight={"bold"} align={"center"} color={"primary"}>
						Your Contact Lists
					</Typography>
					<Typography variant={"body1"} align={"center"}>
						Manage all your imported contact lists here.<br/>
						You can view or delete existing lists, as well as import new ones from a CSV file.
					</Typography>
				</Stack>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs}
															 rows={contactLists}
															 actions={ImportCsvContactListButton()}/>
				</Box>
				<ImportContactListDialog open={openCsvImportDialog}
				                                                 taskId={taskId}
				                                                 setTaskId={setTaskId}
																 checkContactListId={checkContactListId}
																 setCheckContactListId={setCheckContactListId}
																 close={closeCsvImportDialog}
																 success={csvContactListImportSuccessful}/>
                <HubspotColumnSelection open={openColumnSelectionDialog}
				                                                 availableColumns={availableColumns}
																 checkContactListId={checkContactListId}																
																 close={closeOpenColumnSelection}
																 success={ColumnSelectionSuccessful}/>
			</Stack>
		)
	}
}

function ImportContactListDialog(props: {
	open: boolean,
	close: () => void,
	success: () => void,
	checkContactListId: string;
	setCheckContactListId: (id: string) => void;
	taskId: string;
	setTaskId: (id: string) => void;
}) {
	const isProduction = process.env.REACT_APP_BACKEND === "https://api.deliveryman.ai";

	const {enqueueSnackbar} = useSnackbar();

	const [listName, setListName] = useState<string>("");
	const [file, setFile] = useState<File | null>(null);
	const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
	const [contactsData, setContactsData] = useState<string[][]>([]);
	const [emailColumnIndex, setEmailColumnIndex] = useState<number>(0);	
	const [hubspotListId, setHubspotListId] = useState("")
	const [source, setSource] = useState("")

	// Mutation for adding contact list.
	const addContactListMutation = useMutation({
		mutationKey: ["addContactListMutation"],
		mutationFn: () => authenticateAndPostData("/add-contact-list/", {
			name: listName.trim(),
			csv_data: contactsData,
			csv_headers: csvHeaders,
			email_column_index: emailColumnIndex,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar("Success! Your new contact list is being created.", {
				variant: "success",
			});
			props.success();
		},
		onError: (error: ApiRequestFailed) => {
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		},
	})

	const addHubspotContactListMutation = useMutation({
		mutationKey: ["addHubspotContactListMutation"],
		mutationFn: () => authenticateAndPostData("/hubspot/get-all-contact/", {
			name: listName.trim(),
			contactList: hubspotListId,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			enqueueSnackbar("Success! Your new hubspot contact list is being created.", {
				variant: "success",
			});			
			props.setCheckContactListId(response.data["contact_list_id"])
			props.setTaskId(response.data["task_id"])			
			props.success()
		},
		onError: (error: ApiRequestFailed) => {
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		},
	})

	// This will parse the CSV file whenever a new file is selected.
	useEffect(() => {
		if (file) {
			file.text().then(value => {
				let parseResult = Papa.parse(value, {
					skipEmptyLines: "greedy",
				});
				let csvData = parseResult.data as Array<Array<string>>;
				csvData = csvData.filter(value => value.length)

				// Get the headers from first row.
				setCsvHeaders(csvData[0]);
				setContactsData(csvData.slice(1));
			});
		}
	}, [file]);

	const VisuallyHiddenInput = styled('input')({
		clip: 'rect(0 0 0 0)',
		clipPath: 'inset(50%)',
		height: 1,
		overflow: 'hidden',
		position: 'absolute',
		bottom: 0,
		left: 0,
		whiteSpace: 'nowrap',
		width: 1,
	});

	function onFileSelected(event: ChangeEvent<HTMLInputElement>) {
		if (event.target.files) {
			setFile(event.target.files[0]);
		}
	}

	const EmailColumnSelectionForm = () => (
		<FormControl sx={{mt: 4}} fullWidth>
			<InputLabel id="col-select-label">Email Column Name</InputLabel>
			<Select
				labelId="col-select-label"
				value={emailColumnIndex}
				label="Email Column Name"
				onChange={e => setEmailColumnIndex(e.target.value as number)}
			>
				{csvHeaders.map((header, index) => {
					return (
						<MenuItem key={index} value={index}>{header}</MenuItem>
					);
				})}
			</Select>
		</FormControl>
	)

	function confirmButtonHandler() {
		if (source === "Hubspot") {
			addHubspotContactListMutation.mutate();
		} else {
			addContactListMutation.mutate();
		}
	}

	const confirmDisabled =
		listName.trim() === "" ||
		(source === "Hubspot"
			? !hubspotListId || addHubspotContactListMutation.isPending
			: file === null || addContactListMutation.isPending);

	return (
		<Dialog open={props.open}>
			<DialogTitle>Import Contact List from CSV File</DialogTitle>
			<DialogContent>
				<DialogContentText>
					Upload a CSV with maximum 100,000 rows & 20 columns.
				</DialogContentText>
				<Stack direction={"column"} spacing={1} sx={{mt: 2}}>
					<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
						Contact List Name:
					</Typography>
					<TextField value={listName}
										 onChange={e => setListName(e.target.value)}
										 variant="outlined"
										 required/>
				</Stack>
				<Stack direction={"column"} spacing={1} sx={{ mt: 2 }}>
					<Typography variant={"subtitle1"} sx={{ fontWeight: "bold" }}>
						Source:
					</Typography>
					<FormControl fullWidth>
						{/* <InputLabel>Select Source</InputLabel> */}
						<Select
							labelId="source-select-label"
							value={source}
							onChange={(e) => setSource(e.target.value)}
						>
							<MenuItem value="">Select</MenuItem>
							<MenuItem value="CSV">CSV</MenuItem>
							{!isProduction && <MenuItem value="Hubspot">Hubspot</MenuItem>}
						</Select>
					</FormControl>
				</Stack>
				{source === "Hubspot" && (
						<HubspotContact
							hubspotListId={hubspotListId}
							setHubspotListId={setHubspotListId}
						/>
					)}
				{source !== "Hubspot" && (
					<Stack
						display={"flex"}
						flexDirection={"column"}
						justifyContent={"flex-start"}
						alignItems={"flex-start"}
						sx={{ mt: 4 }}
					>
						<Button
							component="label"
							role={undefined}
							variant="contained"
							color={"primary"}
							disabled={addContactListMutation.isPending}
							tabIndex={-1}
							startIcon={<CloudUpload />}
							size={"small"}
						>
							Upload .CSV File
							<VisuallyHiddenInput
								type="file"
								accept={".csv"}
								onChange={onFileSelected}
								multiple
							/>
						</Button>
						<Typography variant="subtitle2" align={"center"} sx={{ mt: 2 }}>
							{file ? `Selected File: ${file.name}` : "No Files Uploaded"}
						</Typography>
						{file !== null && <EmailColumnSelectionForm />}
					</Stack>
				)}
			</DialogContent>
			<DialogActions>
				<Button onClick={props.close} disabled={addContactListMutation.isPending}>Cancel</Button>
				<Button disabled={confirmDisabled} onClick={confirmButtonHandler}>
					{source === "Hubspot"
						? addHubspotContactListMutation.isPending
							? "Fetching from Hubspot..."
							: "Confirm"
						: addContactListMutation.isPending
						? "Uploading. Please Wait..."
						: "Confirm"}
				</Button>
			</DialogActions>
		</Dialog>
	)
}


function HubspotContact({
	hubspotListId,
	setHubspotListId,
}: {
	hubspotListId: string;
	setHubspotListId: (id: string) => void;
}) {
	const [hubspotLists, setHubspotLists] = useState<any[]>([]);

	const pageDataQuery = useQuery({
		queryKey: ["hubspotList"],
		queryFn: () => authenticateAndFetchData("/hubspot/get-hubspot-lists/"),
		retry: retryFn,
		refetchOnWindowFocus: false,
	});

	useEffect(() => {
		if (pageDataQuery.data) {			
			const hubspotLists = pageDataQuery.data.data.lists as any[];			
			setHubspotLists(hubspotLists || []);
		}
	}, [pageDataQuery.data]);


	if (pageDataQuery.isLoading) {		
		return (
			<Stack
				alignItems="center"
				justifyContent="center"
				sx={{ mt: 3, mb: 2 }}
			>
				<l-mirage size="120" color={"#42a5f5"}></l-mirage>				
			</Stack>
		);
	}
	if (!hubspotLists.length) {		
		return (
			<Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
				No List is available on Hubspot.
			</Typography>
		);
	}
	return (
		<Stack spacing={1} sx={{ mt: 2 }}>
			<Typography variant="subtitle1" sx={{ fontWeight: "bold"}}>
				HubSpot Segment List:
			</Typography>
			<Typography variant="body2" color="text.secondary">
				To import leads, please create a list in HubSpot first.  
			</Typography>
			<FormControl fullWidth>
				{/* <InputLabel>Select List</InputLabel> */}
				<Select
					value={hubspotListId}
					onChange={(e) => setHubspotListId(e.target.value)}
				>
					{hubspotLists.map((list: any) => (
						<MenuItem key={list.listId} value={list.listId}>
							{list.name}
						</MenuItem>
					))}
				</Select>
			</FormControl>
		</Stack>
	);

}

function HubspotColumnSelection(props: {
  open: boolean;
  availableColumns: string[];  
  checkContactListId: string;
  close: () => void;
  success: () => void;
}) {
  const [selected, setSelected] = useState<string[]>([]);

  const toggleColumn = (col: string) => {
    setSelected(prev =>
      prev.includes(col) ? prev.filter(c => c !== col) : [...prev, col]
    );
  };

  // Mutation to POST user-selected columns
  const saveSelectionMutation = useMutation({
    mutationKey: ["saveHubspotColumnSelection"],
    mutationFn: () => authenticateAndPostData("/hubspot/save-column-selection/", {
      contact_list_id: props.checkContactListId,
      selected_columns: selected,
    }),
    onSuccess: () => {
      props.close();      
    },
    onError: (err: ApiRequestFailed) => {
      console.error(err);
    }
  });

  return (
    <Dialog open={props.open} onClose={props.close} fullWidth>
      <DialogTitle>Select Columns to Import</DialogTitle>
      <DialogContent>
        <FormGroup>
		{props.availableColumns
			?.slice()
			.sort((a, b) => a.localeCompare(b)) // A-Z sort
			.map((col) => (
			<FormControlLabel
				key={col}
				control={
				<Checkbox
					checked={selected.includes(col)}
					onChange={() => toggleColumn(col)}
				/>
				}
				label={col}
			/>
			))}
		</FormGroup>
      </DialogContent>
      <DialogActions>
        <Button onClick={props.close}>Cancel</Button>
        <Button
          onClick={() => saveSelectionMutation.mutate()}
          disabled={!selected.length || saveSelectionMutation.isPending}
        >
		{saveSelectionMutation.isPending ? 'Fetching Columns...' : 'Import Selected'}          
        </Button>
      </DialogActions>
    </Dialog>
  );
}
