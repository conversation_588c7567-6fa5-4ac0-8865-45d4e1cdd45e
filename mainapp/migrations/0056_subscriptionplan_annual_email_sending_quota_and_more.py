# Generated by Django 5.1.2 on 2025-06-03 13:50

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0055_subscriptionplan_plan_tier'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscriptionplan',
            name='annual_email_sending_quota',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='workspace',
            name='next_renewal_date',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
    ]
