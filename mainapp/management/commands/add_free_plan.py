from django.core.management import BaseCommand

from mainapp.models import SubscriptionPlan


class Command(BaseCommand):
    def handle(self, *args, **options):
        if not SubscriptionPlan.objects.filter(is_free_plan=True).exists():
            SubscriptionPlan.objects.create(
                is_free_plan=True,

                plan_name="Free",
                display_order=0,
                plan_tier=0,
                hidden=True,
                product_id=None,

                monthly_price_id=None,
                annual_price_id=None,

                monthly_amount=0,
                annual_amount=0,

                monthly_feature_list=[],
                annual_feature_list=[],

                popular=False,

                monthly_email_sending_quota=500,
                annual_email_sending_quota=500,
            )

            self.stdout.write(self.style.SUCCESS("[*] Free plan created."))

        else:
            self.stdout.write(self.style.SUCCESS("[x] Free plan already exists."))
