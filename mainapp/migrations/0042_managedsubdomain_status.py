# Generated by Django 5.1.2 on 2025-05-13 13:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0041_rename_block_campaigns_user_is_campaign_blocked'),
    ]

    operations = [
        migrations.AddField(
            model_name='managedsubdomain',
            name='status',
            field=models.CharField(choices=[('warmup', 'Warmup'), ('active', 'Active')], default='active'),
        ),
    ]
