# Generated by Django 5.1.2 on 2025-05-27 08:08

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0046_remove_campaignschedule_email_limit_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_name', models.CharField(max_length=500)),
                ('product_id', models.CharField(max_length=100, null=True)),
                ('created_on', models.DateTimeField(default=django.utils.timezone.now)),
                ('monthly_price_id', models.CharField(max_length=100, null=True)),
                ('annual_price_id', models.Char<PERSON>ield(max_length=100, null=True)),
                ('monthly_amount', models.PositiveIntegerField()),
                ('annual_amount', models.PositiveIntegerField()),
                ('feature_list', models.J<PERSON><PERSON>ield(default=list)),
                ('popular', models.BooleanField(default=False)),
                ('monthly_email_sending_quota', models.PositiveIntegerField()),
            ],
        ),
    ]
