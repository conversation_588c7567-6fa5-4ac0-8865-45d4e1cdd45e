import {<PERSON>, <PERSON><PERSON>, Card, CardContent, MenuItem, Select, Stack, Typography, useTheme} from "@mui/material";
import * as React from "react";
import {useEffect, useState} from "react";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {useMutation, useQuery} from "@tanstack/react-query";
import {useSnackbar} from "notistack";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import {styled} from "@mui/material/styles";
import Dialog from "@mui/material/Dialog";
import {DataGrid, GridColDef} from "@mui/x-data-grid";
import {format} from "date-fns";
import {formatInTimeZone} from "date-fns-tz";

interface PageData {
	status_code: number
	status_message: string

	campaign_name: string
	emails_per_day: number
	total_contacts: number
	total_schedules: number
	total_emails_sent: number
	total_bounced: number
	total_unsubscribes: number
	total_replies: number
	campaign_status: string
	campaign_activities: CampaignActivity[]
	domains: Array<string>
	reply_classification?: "positive" | "neutral" | "negative"
}

interface CampaignActivity {
	id: number
	event_type: string
	event_date_ts: number
	event_subject: string
	event_from: string
	event_additional_data: object
	campaign_schedule_uid: string
}

interface EmailOpenEvent {
	id: number
	contact_email: string
	open_date: number
}

interface EmailOpenEventResponse {
	events: EmailOpenEvent[]
	timezone: string
}

interface LinkClickEvent {
	id: number
	contact_email: string
	link: string
	clicks: number
	latest_date: string
}

interface LinkClickEventResponse {
	events: LinkClickEvent[]
	timezone: string
}

export default function CampaignAnalytics(props: {
	campaignUID: string,
}) {
	const [pageData, setPageData] = useState<PageData>();
	const [positiveReplyCount, setPositiveReplyCount] = useState(0);
	const [negativeReplyCount, setNegativeReplyCount] = useState(0);
	const [neutralReplyCount, setNeutralReplyCount] = useState(0);

	// Query to fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["getCampaignAnalytics"],
		queryFn: () => authenticateAndFetchData(`/campaigns/details/analytics/?campaign_uid=${props.campaignUID}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			const data = pageDataQuery.data.data as PageData
			setPageData(data);
			setPositiveReplyCount(data.campaign_activities.filter(
				activity => activity.event_subject === "Positive Reply").length
			);
			setNeutralReplyCount(data.campaign_activities.filter(
				activity => activity.event_subject === "Neutral Reply").length
			);
			setNegativeReplyCount(data.campaign_activities.filter(
				activity => activity.event_subject === "Negative Reply").length
			);
		}
	}, [pageDataQuery.data]);

	const theme = useTheme();

	const subjectTextColor = (subject: string) => {
		switch (subject) {
			case "Positive Reply":
				return theme.palette.success.main;

			case "Negative Reply":
				return theme.palette.error.main;

			case "Neutral Reply":
				return theme.palette.primary.main;

			case "default":
				return theme.palette.error.main;
		}
	}

	function getLatestStatuses(activities: CampaignActivity[]) {
		const statusMap: Record<string, "Subscribed" | "Unsubscribed"> = {};
		const latestTimestamps: Record<string, number> = {};

		activities.forEach((activity) => {
			const email = activity.event_from;
			const ts = activity.event_date_ts;

			if (!latestTimestamps[email] || ts > latestTimestamps[email]) {
				if (activity.event_subject.includes("Unsubscribe")) {
					statusMap[email] = "Unsubscribed";
				} else if (activity.event_subject.includes("Resubscribe")) {
					statusMap[email] = "Subscribed";
				} else if (activity.event_subject.includes("Reply")) {
					statusMap[email] = "Subscribed";
				}
				latestTimestamps[email] = ts;
			}
		});

		return statusMap;
	}


	const latestStatuses = getLatestStatuses(pageData?.campaign_activities ?? []);

	const columns: GridColDef[] = [
		{
			field: 'activity_text',
			headerName: 'Event',
			editable: false,
			resizable: false,
			renderCell: (params) => {
				return (
					<Box display={"flex"} alignItems={"center"} sx={{height: "100%"}}>
						<Typography>
							Received&nbsp;
							<b>
							<span style={{color: subjectTextColor(params.row.event_subject)}}>
					  		{params.row.event_subject}</span>
							</b> from <b>{params.row.event_from}</b>&nbsp;
							on {format(new Date(params.row.event_date_ts), "do MMM y, HH:mm:ss (xxx)")}
						</Typography>
					</Box>
				)
			},
			flex: 2,
		},
		{
			field: 'action',
			headerName: 'Action',
			headerAlign: "right",
			align: "right",
			editable: false,
			resizable: false,
			renderCell: (params) => {
				if (
					params.row.event_type === "reply" &&
					params.row.event_subject.toLowerCase() !== "bounce" &&
					params.row.event_additional_data["email_s3_key"]
				) {
					// Show reply button.
					return (
						<ViewReply replyFrom={params.row.event_from}
											 s3key={params.row.event_additional_data["email_s3_key"]}/>
					)
				} else {
					// Don't show anything.
					return null;
				}
			},
			flex: 1,
		},
		{
			field: "reply_classification",
			headerName: "Reply Classification",
			headerAlign: "right",
			align: "right",
			editable: false,
			renderCell: (params) => {
				if (params.row.event_type === "reply" && params.row.campaign_schedule_uid) {
					return (
						<ReplyClassificationCell campaign_schedule_uid={params.row.campaign_schedule_uid} uid={params.row.id}
																		 event_subject={params.row.event_subject}
																		 onSuccess={() => {
																			 pageDataQuery.refetch().then();
																		 }}
						/>
					)
				} else {
					<></>
				}
			},
			flex: 1,
		},
		{
			field: "subscribeAction",
			headerName: "Subscribe",
			headerAlign: "right",
			align: "right",
			renderCell: (params) => {
				if (params.row.event_type === "reply" && !params.row.event_subject.includes("Bounce")) {
					return (
						<SubscribeActionCell
							campaignUid={props.campaignUID}
							email={params.row.event_from}
							status={latestStatuses[params.row.event_from] || "Subscribed"}
							onSuccess={() => {
								pageDataQuery.refetch().then();
							}}
						/>
					)
				} else {
					return (
						<></>
					)
				}
			},
			flex: 1,
		}
	];

	const paginationModel = {page: 0, pageSize: 10};

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack spacing={4} direction={"column"} justifyContent={"center"} alignItems={"center"} sx={{mb: 6}}>
				<Box sx={{display: "grid", gridTemplateColumns: "repeat(6, 1fr)", gridGap: "10px", width: "100%"}}>
					{/* Total Contacts */}
					<Card sx={{borderRadius: 4, flexGrow: 1, minHeight: "120px"}}>
						<CardContent>
							<Typography variant={"body1"} align={"left"} color={"textSecondary"}>Total Contacts</Typography>
							<Typography variant={"h6"} align={"left"}>{pageData.total_contacts}</Typography>
						</CardContent>
					</Card>
					{/* Emails per Day */}
					<Card sx={{borderRadius: 4, flexGrow: 1, minHeight: "120px"}}>
						<CardContent>
							<Typography variant={"body1"} align={"left"} color={"textSecondary"}>Emails Per Day</Typography>
							<Typography variant={"h6"} align={"left"}>
								{pageData.emails_per_day > 0 ? pageData.emails_per_day : "Auto"}
							</Typography>
						</CardContent>
					</Card>
					{/* Total Emails Sent */}
					<Card sx={{borderRadius: 4, flexGrow: 1, minHeight: "120px"}}>
						<CardContent>
							<Typography variant={"body1"} align={"left"} color={"textSecondary"}>Total Emails Sent</Typography>
							<Typography variant={"h6"} align={"left"}>{pageData.total_emails_sent}</Typography>
						</CardContent>
					</Card>
					{/* Total Bounces */}
					<Card sx={{borderRadius: 4, flexGrow: 1, minHeight: "120px"}}>
						<CardContent>
							<Typography variant={"body1"} align={"left"} color={"textSecondary"}>Total Bounces</Typography>
							<Typography variant={"h6"} align={"left"}>
								{pageData.total_bounced}&nbsp;
							</Typography>
						</CardContent>
					</Card>
					{/* Total Replies */}
					<Card sx={{borderRadius: 4, flexGrow: 1, minHeight: "120px"}}>
						<CardContent>
							<Typography variant={"body1"} align={"left"} color={"textSecondary"}>Total Replies</Typography>
							<Typography variant={"h6"} align={"left"}>
								{pageData.total_replies}&nbsp;
							</Typography>
							<Typography variant={"caption"} sx={{fontSize: "0.7em"}} color={"textSecondary"}>
								Positive: {positiveReplyCount} | Neutral: {neutralReplyCount} | Negative: {negativeReplyCount}
							</Typography>
						</CardContent>
					</Card>
					{/* Total Unsubscribes */}
					<Card sx={{borderRadius: 4, flexGrow: 1, minHeight: "120px"}}>
						<CardContent>
							<Typography variant={"body1"} align={"left"} color={"textSecondary"}>Total Unsubscribed</Typography>
							<Typography variant={"h6"} align={"left"}>
								{pageData.total_unsubscribes}&nbsp;
							</Typography>
						</CardContent>
					</Card>
				</Box>

				{/* ---------------- Activity ---------------- */}
				<Box sx={{width: "100%", mt: 4}}>
					<Stack direction={"column"} sx={{width: "100%"}}>
						<Typography variant={"h6"}>Reply Activity</Typography>
						{/*<Divider orientation={"horizontal"} sx={{width: "100%", mt: 2}}/>*/}
						<DataGrid
							rows={pageData.campaign_activities}
							columns={columns}
							localeText={{
								noRowsLabel: "No Activity"
							}}
							pageSizeOptions={[10, 50, 100]}
							initialState={{pagination: {paginationModel}}}
							sx={{mt: 4}}
						/>
					</Stack>
				</Box>

				{/* ---------------- Open Events ---------------- */}
				<EmailOpenEventsTable campaignUID={props.campaignUID}/>

				{/* ---------------- Click Events ---------------- */}
				<LinkClickEventsTable campaignUID={props.campaignUID}/>

				{/* Alternate solution for bottom margin. I don't know why margin-bottom style is not working */}
				<Box sx={{height: "40px"}}></Box>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}

const BootstrapDialog = styled(Dialog)(({theme}) => ({
	'& .MuiDialogContent-root': {
		padding: theme.spacing(2),
	},
	'& .MuiDialogActions-root': {
		padding: theme.spacing(1),
	},
}));

function ViewReply(props: {
	replyFrom: string,
	s3key: string,
}) {
	const [open, setOpen] = React.useState(false);
	const [replyMessage, setReplyMessage] = React.useState("");

	// Query to fetch reply email message.
	const replyMessageQuery = useQuery({
		queryKey: ["replyMessageQuery", props.replyFrom, props.s3key],
		queryFn: () => authenticateAndFetchData(`/campaigns/get-reply-email-message/?key=${props.s3key}`),
		gcTime: 5000,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (replyMessageQuery.data) {
			setReplyMessage(replyMessageQuery.data.data["message"]);
		}
	}, [replyMessageQuery.data]);

	const handleClickOpen = () => {
		setOpen(true);
	};
	const handleClose = () => {
		setOpen(false);
	};

	if (replyMessageQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else {
		return (
			<React.Fragment>
				<Button onClick={handleClickOpen}>
					View Reply
				</Button>
				<BootstrapDialog
					onClose={handleClose}
					aria-labelledby="view-reply-dialog"
					open={open}
				>
					<DialogTitle sx={{m: 0, p: 2}} id="view-reply-dialog">
						Reply from <b>{props.replyFrom}</b>
					</DialogTitle>
					<DialogContent dividers>
						<Typography gutterBottom>
							<pre style={{whiteSpace: "pre-wrap", wordWrap: "break-word"}}>{replyMessage}</pre>
						</Typography>
					</DialogContent>
					<DialogActions>
						<Button autoFocus onClick={handleClose}>
							Close
						</Button>
					</DialogActions>
				</BootstrapDialog>
			</React.Fragment>
		);
	}
}

const ReplyClassificationCell = (props: {
	campaign_schedule_uid: string;
	uid: string;
	event_subject: string;
	onSuccess: () => void,
}) => {
	const {enqueueSnackbar} = useSnackbar();

	// Map event_subject → default value
	const getDefaultValue = (subject: string) => {
		if (subject.includes("Positive Reply")) return "positive";
		if (subject.includes("Negative Reply")) return "negative";
		if (subject.includes("Neutral Reply")) return "neutral";
		return "";
	};

	const [value, setValue] = useState(getDefaultValue(props.event_subject));
	const updateReplyClassificationMutation = useMutation({
		mutationKey: ["updateReplyClassification", props.uid],
		mutationFn: (reply: string) =>
			authenticateAndPostData("/update-reply-classification/", {
				campaignActivityUid: props.uid,
				campaignScheduleUid: props.campaign_schedule_uid,
				reply,
			}),
		gcTime: 0,
		retry: 1,
		onSuccess: (data: any) => {
			setValue(data.data.reply_classification);
			enqueueSnackbar("Reply classification updated", {variant: "success"});
			props.onSuccess();
		},
		onError: (error: any) => {
			enqueueSnackbar(error.data?.message || "Failed to update reply classification", {
				variant: "error",
			});
			console.error(error);
		},
	});

	return (
		<Select
			size="small"
			value={value}
			onChange={(e) => {
				const newValue = e.target.value;
				setValue(newValue); // update local UI immediately
				updateReplyClassificationMutation.mutate(newValue);
			}}
		>
			<MenuItem value="positive">Positive</MenuItem>
			<MenuItem value="negative">Negative</MenuItem>
			<MenuItem value="neutral">Neutral</MenuItem>
		</Select>
	);
};


const SubscribeActionCell = (props: {
	campaignUid: string;
	email: string;
	status: "Subscribed" | "Unsubscribed";
	onSuccess: () => void,
}) => {
	const {enqueueSnackbar} = useSnackbar();

	// if currently unsubscribed → show resubscribe button
	const isUnsubscribed = props.status === "Unsubscribed";

	const subscribeMutation = useMutation({
		mutationKey: ["SubscribeAndUnsubscribe", props.email],
		mutationFn: (action: string) =>
			authenticateAndPostData("/subscribe-and-unsubscribe/", {
				campaignUid: props.campaignUid,
				email: props.email,
				action,
			}),
		onSuccess: (_, action) => {
			enqueueSnackbar(
				action === "unsubscribe"
					? `${props.email} unsubscribed.`
					: `${props.email} resubscribed.`,
				{variant: "success"}
			);
			props.onSuccess();
		},
		onError: (error: any) => {
			enqueueSnackbar(error.data?.message || "Failed to update subscription", {
				variant: "error",
			});
		},
	});

	const handleClick = () => {
		const action = isUnsubscribed ? "resubscribe" : "unsubscribe";
		subscribeMutation.mutate(action);
	};

	return (
		<Button
			size="small"
			color={isUnsubscribed ? "success" : "error"}
			onClick={handleClick}
			disabled={subscribeMutation.isPending}
		>
			{isUnsubscribed ? "Resubscribe" : "Unsubscribe"}
		</Button>
	);
};


const EmailOpenEventsTable = (props: {
	campaignUID: string
}) => {
	const [tableData, setTableData] = useState<EmailOpenEvent[]>([]);
	const [timezone, setTimezone] = useState<string>("UTC");

	const paginationModel = {page: 0, pageSize: 10};

	// Query for fetching email open event data.
	const fetchEmailOpenEventDataQuery = useQuery({
		queryKey: ["fetchEmailOpenEventDataQuery"],
		queryFn: () => authenticateAndFetchData(
			`/campaigns/details/analytics/open-events/?campaign_uid=${props.campaignUID}`
		),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (fetchEmailOpenEventDataQuery.data) {
			let data: EmailOpenEventResponse = fetchEmailOpenEventDataQuery.data.data;
			setTableData(data.events);
			setTimezone(data.timezone);
		}
	}, [fetchEmailOpenEventDataQuery.data]);

	function getDateString(datestring: string) {
		return formatInTimeZone(new Date(datestring), timezone, "do MMM yyyy, hh:mm a").replace(
			/\b(AM|PM)\b/, (match) => match.toLowerCase()
		);
	}

	// Columns for table.
	const columns: GridColDef[] = [
		{
			field: 'event',
			headerName: 'Event',
			editable: false,
			resizable: false,
			renderCell: (params) => {
				return (
					<>
						<b>{params.row.contact_email}</b> opened your email on <b>{getDateString(params.row.open_date)}</b>
					</>
				)
			},
			flex: 1,
		},
	];

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Box sx={{width: "100%", mt: 4}}>
			<Stack direction={"column"} sx={{width: "100%"}}>
				<Typography variant={"h6"}>Email Open Events</Typography>
				<DataGrid
					rows={tableData}
					columns={columns}
					localeText={{
						noRowsLabel: "No Events Available"
					}}
					pageSizeOptions={[10, 50, 100]}
					initialState={{pagination: {paginationModel}}}
					sx={{mt: 4}}
				/>
			</Stack>
		</Box>
	)
}

const LinkClickEventsTable = (props: {
	campaignUID: string
}) => {
	const [tableData, setTableData] = useState<LinkClickEvent[]>([]);
	const [timezone, setTimezone] = useState<string>("UTC");

	const paginationModel = {page: 0, pageSize: 10};

	// Query for fetching email open event data.
	const fetchLinkClickEventDataQuery = useQuery({
		queryKey: ["fetchLinkClickEventDataQuery"],
		queryFn: () => authenticateAndFetchData(
			`/campaigns/details/analytics/click-events/?campaign_uid=${props.campaignUID}`
		),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (fetchLinkClickEventDataQuery.data) {
			let data: LinkClickEventResponse = fetchLinkClickEventDataQuery.data.data;
			setTableData(data.events);
			setTimezone(data.timezone);
		}
	}, [fetchLinkClickEventDataQuery.data]);

	function getDateString(datestring: string) {
		return formatInTimeZone(new Date(datestring), timezone, "do MMM yyyy, hh:mm a").replace(
			/\b(AM|PM)\b/, (match) => match.toLowerCase()
		);
	}

	// Columns for table.
	const columns: GridColDef[] = [
		{
			field: 'event',
			headerName: 'Event',
			editable: false,
			resizable: false,
			renderCell: (params) => {
				return (
					<>
						<b>{params.row.contact_email}</b> clicked on&nbsp;
						<span style={{textDecoration: "underline"}}>{params.row.link}</span>&nbsp;
						<b>{params.row.clicks}</b> times
						in your email (latest at <b>{getDateString(params.row.latest_date)}</b>)
					</>
				)
			},
			flex: 1,
		},
	];

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Box sx={{width: "100%", mt: 4}}>
			<Stack direction={"column"} sx={{width: "100%"}}>
				<Typography variant={"h6"}>Link Click Events</Typography>
				<DataGrid
					rows={tableData}
					columns={columns}
					localeText={{
						noRowsLabel: "No Events Available"
					}}
					pageSizeOptions={[10, 50, 100]}
					initialState={{pagination: {paginationModel}}}
					sx={{mt: 4}}
				/>
			</Stack>
		</Box>
	)
}
