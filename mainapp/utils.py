import datetime
import json
import logging
import os
import time
import pickle
import re
import dns.resolver
from email import policy
from email.message import Message
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.parser import By<PERSON><PERSON>ars<PERSON>
from typing import List, Dict, Literal, Optional, Tuple

import boto3
import csv
import requests
from requests.exceptions import HTTPError
import tldextract
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from cryptography.fernet import Fernet, InvalidToken
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.utils import timezone
from google.auth.exceptions import RefreshError
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from pydantic import TypeAdapter, BaseModel
from retry import retry

from ColdEmailerBackend.settings import DEBUG, openai_client, google_api_service, WARMUP_EMAILS_MAX
from mainapp.models import User, Campaign, ManagedSubdomain, EmailID, WarmupSchedule, HubspotIntegration
from mainapp.pydantic_models import GooglePostmasterTrafficDataSchema, GetPostmasterDataSchema, EmailContentCheck

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


def build_email_subdomains(fqdn: str, amount: int, names_list: List[str], last_subdomain=None) -> List[str]:
    """
    Generates given amount of email subdomains. If adding more subdomains provide the "last_subdomain" value and it
    will continue from there.

    IMPORTANT: This might generate duplicate subdomains if the "names_list" is rearranged.

    :param fqdn: fully qualified domain name (ex. hey.draftss.com)
    :param amount: Number of subdomains to generate
    :param names_list: List of words/names (english alphabets only) to use in subdomain generation.
    :param last_subdomain: Do not provide this value if this is the first time generating subdomains for this fqdn.
    :returns: List of subdomains.
    """
    current_index = 0
    numbering = 0
    subdomains: List[str] = []

    if last_subdomain is not None:
        # We'll continue where we left off.
        # using match here since we need to look from start of string.
        letter_match = re.match(r"^([a-zA-Z]+)", last_subdomain.split(".")[0])
        # using search here since number won't be present at start of string.
        number_match = re.search(r"(\d+)", last_subdomain.split(".")[0])

        if number_match:
            numbering = int(number_match.group(1))

        try:
            current_index: int = names_list.index(letter_match.group(1)) + 1
        except ValueError:
            # Gets raised if name could not be found in list.
            # In this case we'll get the number part, increment it by 1 and start fresh from beginning of list.
            current_index = 0
            numbering += 1

    for i in range(amount):
        # This will increment but reset to 0 if over length of names list.
        if current_index >= len(names_list):
            current_index = 0
            numbering += 1

        # TODO: We should add a check here to make sure this subdomain is not already present.
        subdomains.append(f"{names_list[current_index]}{numbering or ''}.{fqdn}")

        current_index += 1

    return subdomains


# def remove_duplicate_csv_rows(csv_data: List[List[str]]) -> List[List[str]]:
#     """
#     Removes duplicate rows from CSV data.
#
#     :param csv_data: A list of lists, where each inner list represents a row of strings.
#
#     :returns: A new list of lists with duplicate rows removed.
#     """
#     # Use a set to store unique rows. Tuples are used because lists are unhashable.
#     unique_rows = set(tuple(row) for row in csv_data)
#     # Convert the unique tuples back to lists and return.
#     return [list(row) for row in unique_rows]

def remove_duplicate_rows(csv_data: list[list[str]], email_column_index: int) -> list[list[str]]:
    """
    Removes duplicate rows from CSV data based on the email column.

    :param csv_data: A list of lists, where each inner list represents a row of strings.
    :param email_column_index: Index of email column in csv data.

    :returns: A new list of lists with duplicate rows removed.
    """
    unique_emails = set()
    deduplicated_data = []

    # Iterate through each row in the CSV data
    for row in csv_data:
        email = row[email_column_index]
        # If the email is not already in our set of unique emails,
        # add it to the set and add the entire row to our deduplicated list.
        if email not in unique_emails:
            unique_emails.add(email)
            deduplicated_data.append(row)

    return deduplicated_data


def csv_rows_to_dict(csv_data: List[List[str]], headers: List[str]) -> List[Dict]:
    """
    Converts a CSV 2D list data to a list of dictionaries. Each key is the column name.

    :param csv_data: 2D list of string excluding header row.
    :param headers: All column names as 1D list of string.
    """
    dict_data: List[Dict] = []

    for row in csv_data:
        data = {}
        for index, header in enumerate(headers):
            try:
                data.update({header: row[index]})
            except IndexError:
                logger.error(csv_data)
                logger.error(headers)
                raise Exception(f"Number of values in row does not match the number of headers. "
                                f"Row values: {len(row)} Headers: {len(headers)}")

        dict_data.append(data)

    return dict_data


def extract_message_id(in_reply_to_string: str) -> str:
    """
    Extracts the original email's Message ID from a reply email "in-reply-to" header data.

    :param in_reply_to_string: Value of "in-reply-to" key.
    :returns: Message ID if found. Otherwise empty string.
    """
    match = re.search(r'<([^@]+)@', in_reply_to_string)
    if match:
        return match.group(1)
    else:
        return ""


@retry(Exception, tries=3, backoff=3)
def classify_email_reply(plaintext_email_message: str) -> Literal["positive", "neutral", "negative"]:
    """
    Runs an LLM to classify the nature of reply message in given mailparser text_plain data. In case of any error, will
    retry 3 times with backoff 2 seconds.

    :param plaintext_email_message: Result of mail.text_plain[0] using mailparser library.
    :return: "positive" | "neutral" | "negative"
    """
    # ---------------- System Prompt ----------------
    system_prompt: str = """You are an expert at extracting the reply message from given raw email data and classifying the nature of reply. Your job is to classify the nature of reply message to a campaign email. Let's do this step by step.

First you need to check if this a bounced email reply. Usually bounce replies will mention "address not found", "address misspelled", "blocked", "access denied", "mailbox full" or some similar text from mailer system. If this is the case, your response should be {"reply_class": "bounced"}.

If it's not a bounce, follow the remaining steps given below:
First, you need to extract the reply message from given raw email data. The format for raw email data is given below.
```
[user_reply]

    On [datetime], [original_sender_name] [<original_sender_email>] wrote:


[original_sender_message]
```

Next and finally, you need to classify the user reply message. If the reply is a out of office message then it is neutral. If the reply is asking to stop or unsubscribe then negative. Classify it into one of the following category.
- Positive
- Neutral
- Negative

---

Your response should be in below given JSON format:
{"reply_class": "positive" | "neutral" | "negative" | "bounced"}

Do not add any text or remark before or after the JSON response."""

    # ---------------- User Prompt ----------------
    user_prompt: str = f"""Raw Email Data:
```
{plaintext_email_message} 
```"""

    # Run the LLM with system and user prompts and parse the JSON result. If there is any JSON parsing error,
    # then retry for a few more times.
    chat_completion = openai_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": system_prompt,
            },
            {
                "role": "user",
                "content": user_prompt,
            }
        ],
        temperature=0.1,
    )
    response: str = chat_completion.choices[0].message.content
    logger.debug(f"Email Reply Classification Response: {response}")

    # Make sure the reply class is corrcet.
    reply_class = json.loads(response)["reply_class"]
    if reply_class not in ["positive", "neutral", "negative"]:
        raise Exception(f"Invalid reply class from LLM: {reply_class}")

    return reply_class


@retry(Exception, tries=3, backoff=2)
def send_verify_emails_request(emails: List[str]) -> str:
    """
    Makes a request to bulk verify api on mailer.draftss.com.

    :param emails: List of email addresses.
    :return: Task UID for webhook identification.
    """
    if DEBUG:
        wh_url: str = "https://aicaller-backend.abun.com/webhook/email-verify/"
    else:
        wh_url: str = "https://api.deliveryman.ai/webhook/email-verify/"

    res = requests.post(
        url="https://mailer.draftss.com/bulk-verify-emails",
        json={"emails": emails, "webhook_url": wh_url},
        timeout=10,
    )
    if res.status_code != 200:
        raise Exception(f"Email Verify api call failed with status code {res.status_code}: {res.text}")
    else:
        result = res.json()
        return result["task_uid"]


def send_websocket_event(group_name: str, function_name: str, event_name: str, event_data: dict):
    """
    Sends websocket message for given group and function.

    :param group_name: Websocket group name.
    :param function_name: Websocket class sending function name.
    :param event_name: (for client) event name.
    :param event_data: (for client) event json data.
    """
    channel_layer = get_channel_layer()
    logger.debug(channel_layer)
    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            "type": function_name,
            "data": {
                "event_name": event_name,
                "event_data": event_data
            }
        }
    )
    logger.debug(f"Websocket event '{event_name}' sent to group '{group_name}'")


def get_postmaster_verification_token(domain: str) -> str:
    """
    Returns verification token to add in TXT record.
    """
    request_body = {
        'site': {'type': 'INET_DOMAIN', 'identifier': domain},
        'verificationMethod': 'DNS_TXT'
    }
    response = google_api_service.webResource().getToken(body=request_body).execute()
    token: str = response['token']
    return token


def verify_postmaster_domain(domain: str) -> bool:
    """
    Verifies given domain so it can be used in Google Postmaster. Use after adding TXT record
    using `get_postmaster_verification_token` function.
    """
    request_body = {'site': {'type': 'INET_DOMAIN', 'identifier': domain}}
    try:
        google_api_service.webResource().insert(verificationMethod='DNS_TXT', body=request_body).execute()
        print(f"Domain {domain} verified successfully.")
        return True
    except Exception as e:
        print(f"Verification failed: {e}")
        return False


def get_postmaster_data(user: User, domain: str) -> GetPostmasterDataSchema:
    """
    Fetches postmaster data for given domain.

    :param user: User model object.
    :param domain: ex. draftss.com
    """
    try:
        ta = TypeAdapter(List[GooglePostmasterTrafficDataSchema])
        if not user.google_postmaster_integration:
            # No integration.
            return GetPostmasterDataSchema(
                integration_active=False,
                httpError=False,
                domain_reputation=None,
                spam_ratio=0.0,
            )

        credentials = pickle.loads(user.google_api_auth_creds)
        if not credentials.valid:
            if credentials.expired and credentials.refresh_token:
                # Renew the tokens.
                try:
                    credentials.refresh(Request())

                except RefreshError as err:
                    logger.error(f"Postmaster google auth refresh token expired for user {user.email}: {err}")

                    user.google_auth_request_uid = None
                    user.google_postmaster_integration.delete()
                    user.save()

                    return GetPostmasterDataSchema(
                        integration_active=False,
                        httpError=False,
                        domain_reputation=None,
                        spam_ratio=0.0,
                    )

        service = build(
            'gmailpostmastertools',
            'v1beta1',
            credentials=credentials
        )

        try:
            res: Dict = service.domains().trafficStats().list(
                parent=f'domains/{domain}'
            ).execute()
            data: List[GooglePostmasterTrafficDataSchema] = ta.validate_python(res["trafficStats"])

        except HttpError:
            # This can happen when domain has not been added to postmaster or if there is no data available.
            logger.warning(f"Postmaster data fetch failed for {domain}")
            return GetPostmasterDataSchema(
                integration_active=False,
                httpError=True,
                domain_reputation=None,
                spam_ratio=0.0,
            )

        # Fetch the domain reputation from latest data.
        domain_reputation = data[-1].domainReputation

        # Fetch the spam ratio.
        today = timezone.now().date()
        highest_spam_ratio: float = 0.0
        for item in data:
            data_date = datetime.datetime.strptime(item.name.split("/")[-1], "%Y%m%d").date()
            if (today - data_date).days <= 14:
                highest_spam_ratio = max(highest_spam_ratio, item.userReportedSpamRatio or 0.0)
                logger.debug(f"Highest spam ratio -> {highest_spam_ratio}")

        return GetPostmasterDataSchema(
            integration_active=True,
            httpError=False,
            domain_reputation=domain_reputation,
            spam_ratio=highest_spam_ratio,
        )

    except Exception as err:
        logger.critical(f"Failed to fetch Postmaster data: {err}", exc_info=True)
        return GetPostmasterDataSchema(
            integration_active=True,
            httpError=True,
            domain_reputation=None,
            spam_ratio=0.0,
        )


def get_email_body(raw_email_content: bytes):
    """
    Works on raw email and returns the body section as plain text.

    :param raw_email_content: Raw email message data.
    """
    plain_text = None
    html_content = None

    msg: Message = BytesParser(policy=policy.default).parsebytes(raw_email_content)

    if msg.is_multipart():
        for part in msg.walk():
            content_type = part.get_content_type()

            if content_type == "text/plain":
                print("Got plain text body content!")
                payload = part.get_payload(decode=True)
                if payload:
                    plain_text = payload.decode()
                    break

            elif content_type == "text/html":
                print("Got HTML body content!")
                payload = part.get_payload(decode=True)
                if payload:
                    html_content = payload.decode()
                    break

        return plain_text, html_content

    else:
        content_type = msg.get_content_type()

        if content_type == "text/plain":
            return msg.get_payload(decode=True).decode(), None

        elif content_type == "text/html":
            return None, msg.get_payload(decode=True).decode()

        else:
            return None, None


def send_email(to: str | List,
               sender: str,
               sender_name: str,
               subject: str,
               body_html: str | None = None,
               body_plaintext: str | None = None,
               files: Optional[List[InMemoryUploadedFile]] = None,
               reply_to: Optional[str] = None,
               region_name="us-east-1"):
    """
    Sends email through AWS SES. Supports both single and multiple recipients.

    :param to: Recipient email address. Can be a string or list of strings.
    :param sender: Sender (our) email address.
    :param sender_name: Sender name.
    :param subject: Subject of email.
    :param body_html: Body (HTML/plaintext) content of email.
    :param body_plaintext:
    :param reply_to: Reply-To email address.
    :param files:
    :param reply_to:
    :param region_name:
    :return:
    """
    if (body_html is None) and (body_plaintext is None):
        raise Exception("Please provide at least one or both of `body_html` and `body_plaintext`")

    CHARSET = "UTF-8"

    msg = MIMEMultipart()
    msg['Subject'] = subject
    msg['From'] = f"{sender_name} <{sender}>"
    msg['Reply-To'] = reply_to

    if isinstance(to, str):
        msg['To'] = to
    else:
        msg['To'] = ', '.join(to)

    if body_plaintext:
        plaintext_part = MIMEText(body_plaintext, 'plain', CHARSET)
        msg.attach(plaintext_part)

    if body_html:
        html_part = MIMEText(body_html, 'html', CHARSET)
        msg.attach(html_part)

    if files:
        for file in files:
            attachment = MIMEApplication(file.open().read())
            attachment.add_header('Content-Disposition', 'attachment', filename=file.name)
            msg.attach(attachment)

    # We only need v1 version here. So didn't add to settings.py
    client = boto3.client('ses', region_name=region_name)
    client.send_raw_email(
        Source=f"{sender_name} <{sender}>",
        Destinations=[to] if isinstance(to, str) else to,
        RawMessage={
            'Data': msg.as_string(),
        },
    )


class AreCampaignEmailsValidResult(BaseModel):
    valid: bool
    reason: str


def are_campaign_emails_valid(campaign: Campaign) -> AreCampaignEmailsValidResult:
    """
    Run this before starting or shceduling campaigns. Checks if all email messages are valid.

    :param campaign: Campaign object.
    """
    # Check for number of emails in campaign.
    if campaign.campaignemailmessage_set.count() == 0:
        return AreCampaignEmailsValidResult(
            valid=False,
            reason="Please add at least one email message in the campaign before starting."
        )

    # Check if any email message is blocked or rejected.
    if campaign.campaignemailmessage_set.filter(label__in=["blocked", "rejected"]).count() > 0:
        return AreCampaignEmailsValidResult(
            valid=False,
            reason="Cannot start/schedule campaign. One or more of your email messages are currently blocked due to our content filters."
        )

    # Check campaign's current state.
    if campaign.status != "created":
        return AreCampaignEmailsValidResult(
            valid=False,
            reason="Campaign is not in 'created' state."
        )

    # Check for subject/body messages and spam score of emails.
    for email_message in campaign.campaignemailmessage_set.all():
        if not email_message.body:
            return AreCampaignEmailsValidResult(
                valid=False,
                reason="Email body cannot be empty."
            )

        if not email_message.subject:
            return AreCampaignEmailsValidResult(
                valid=False,
                reason="Email subject cannot be empty."
            )

        if (email_message.score is None) or (email_message.score < 75):
            return AreCampaignEmailsValidResult(
                valid=False,
                reason=("One or more of your email messages fall below the required 75% Email Score. "
                        "Please improve these emails before running campaign.")
            )

    return AreCampaignEmailsValidResult(
        valid=True,
        reason="",
    )


def get_schedule_times(number_of_emails: int) -> List[datetime]:
    """
    Generates schedule datetimes based on given number of emails, by equally distributing them among remaining hour.

    :param number_of_emails: Number of emails to schedule for.
    :returns: List of datetime objects.
    """
    if number_of_emails == 0:
        return []

    # Get the current datetime in the UTC timezone.
    current_time = datetime.datetime.now(tz=datetime.timezone.utc)

    # Create a datetime for the end of today (midnight).
    end_of_day = datetime.datetime.now(datetime.timezone.utc).replace(hour=23, minute=59, second=0)

    # Calculate the remaining hours till midnight.
    hours_remaining: float = (end_of_day - current_time).total_seconds() / 3600
    print(f"Hours Remaining: {hours_remaining}")

    # Calculate how many hours of gap is required between each schedule for equal distribution.
    hours_gap: float = hours_remaining / number_of_emails
    print(f"Hours Gap: {hours_gap}")

    # Create a list of times for schedule.
    next_datetime = current_time
    dates = []
    for i in range(number_of_emails):
        next_datetime += datetime.timedelta(hours=hours_gap)
        dates.append(next_datetime)

    return dates


def create_warmup_schedules(managed_subdomain: ManagedSubdomain):
    """
    Schedules email warmup and increments `warmup_email_count` for given managed domain/subdomain.

    :param managed_subdomain: ManagedSubdomain model object.
    """
    increase_value: float = float(os.environ["CE_EMAIL_WARMUP_INCREASE"])

    current_time = datetime.datetime.now(tz=datetime.timezone.utc)
    end_of_day = datetime.datetime.now(datetime.timezone.utc).replace(hour=23, minute=59, second=0)

    # Calculate number of hours remaining.
    hours_remaining: float = (end_of_day - current_time).total_seconds() / 3600

    # Calculate number of emails to schedule for based on remaining time.
    emails_to_schedule: int = round(managed_subdomain.warmup_email_count / 24 * hours_remaining)

    if emails_to_schedule > 0:
        logger.debug(f"[*] Creating {emails_to_schedule} schedules for {managed_subdomain.subdomain}...")

        # Get all schedule dates.
        schedule_dates = get_schedule_times(emails_to_schedule)

        # Select an email for warmup.
        try:
            email: EmailID = EmailID.objects.filter(email_subdomain__managed_subdomain=managed_subdomain)[0]
        except IndexError:
            logger.error(
                f"[x] Index Error: Failed to fetch email from managed subdomain {managed_subdomain.subdomain}.")
            return

        # Create warmup shcedules.
        for date in schedule_dates:
            WarmupSchedule.objects.create(
                email=email,
                schedule_datetime=date
            )
            logger.debug(f"[*] Schedule created for email {email.email_address} "
                         f"at {date.strftime('%a, %d %b %Y %H:%M:%S %Z')}")

        # Update warmup email count for next day.
        warmup_email_count: float = managed_subdomain.warmup_email_count
        # Cap to WARMUP_EMAILS_MAX.
        new_count: float = min((increase_value * warmup_email_count) + warmup_email_count, WARMUP_EMAILS_MAX)
        managed_subdomain.warmup_email_count = new_count
        managed_subdomain.save()
        logger.debug(f"[*] New email count for {managed_subdomain.subdomain}: {managed_subdomain.warmup_email_count}")

    else:
        logger.debug("[*] No emails can be scheduled right now for warmup.")


# def get_domain_sending_limit(msub: ManagedSubdomain, campaign: Campaign | None = None,
#                              date: Union[datetime, None] = None) -> int:
#     """
#     Returns number of emails that can be sent for emails from this domain for the day.
#
#     :param msub: ManagedSubdomain model object.
#     :param campaign: Campaign model object.
#     :param date: Date to check against. If None, current time will be used.
#     :return: Number of emails that can be sent.
#     """
#     if date:
#         today = date
#     else:
#         today = timezone.now()
#
#     if today < msub.domain_usable_from:
#         return 0
#
#     # sending_limits: SendingLimitRuleList = SendingLimitRuleList.model_validate(
#     #     SiteConfiguration.objects.all()[0].domain_sending_limits
#     # )
#     #
#     # rule: SendingLimitRule
#     # limit: int | None = None
#     # for rule in sending_limits:
#     #     if (msub.domain_usable_from + datetime.timedelta(days=rule.days)) > today:
#     #         limit = rule.emails
#     #         break
#     #
#     # # If all rule days have been crossed, use value from last rule.
#     # if limit is None:
#     #     limit = sending_limits[-1].emails
#
#     # Calculate how many days have passed since domain was made usable.
#     days_passed_since_usable: int = (today - msub.domain_usable_from).days
#
#     # For each day do
#
#     # Check if user has defined any limit during campaign creation.
#     if campaign is not None:
#         if (campaign.emails_per_day > 0) and (campaign.emails_per_day < limit):
#             limit = campaign.emails_per_day
#
#     return limit


def check_email_contents(subject: str, body: str) -> EmailContentCheck:
    """
    Runs LLM for checking scam/phishing/explicit contents in email subject and body. Return EmailContentCheck pydantic
    object with fields "reject": bool and "reason": str.

    :param subject: Email subject text.
    :param body: Email body text.
    """
    with open("mainapp/prompts/email_content_check_system_prompt.txt", "r") as f:
        system_prompt: str = f.read()

    chat_completion = openai_client.beta.chat.completions.parse(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": system_prompt,
            },
            {
                "role": "user",
                "content": json.dumps({
                    "email_subject": subject,
                    "email_body": body
                }),
            }
        ],
        response_format=EmailContentCheck,
    )
    result: EmailContentCheck = EmailContentCheck.model_validate(
        json.loads(chat_completion.choices[0].message.content)
    )

    return result


def is_valid_domain(value: str) -> bool:
    """
    Validate if a string is a properly formatted domain name. Rejects IP addresses.

    :param value: The domain string to validate
    :returns: True if the domain is valid, False otherwise
    """
    if not value or len(value) > 253:
        return False

    domain_pattern = r'(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]'

    if not bool(re.match(domain_pattern, value)):
        return False

    # Check for things like ip address that don't get caught by above pattern.
    domain_data = tldextract.extract(value)
    if not (domain_data.domain and domain_data.suffix):
        return False

    return True


def get_ip_country(ip_address: str) -> str:
    """
    Returns location (country name) of given ip address.

    :param ip_address: IP address value.
    :returns: Country name (string)
    """
    res = requests.get(f"http://ip-api.com/json/{ip_address}")
    if res.status_code == 200:
        return res.json()["country"]

    else:
        raise Exception(f"ip-api.com request failed. Status Code: {res.status_code}")


def is_domain_blacklisted(domain):
    dnsbls = [
        "zen.spamhaus.org",
        "bl.spamcop.net",
        "b.barracudacentral.org",
        "cbl.abuseat.org",
        # Add more DNSBLs you want to check
    ]

    listed_on = []

    for dnsbl in dnsbls:
        # For domains, some DNSBLs expect the domain as-is,
        # others might expect a reversed IP if you're checking an associated IP.
        # This example assumes domain lookup.
        # For IP lookups, you'd reverse the IP octets.
        query = f"{domain}.{dnsbl}"

        try:
            answers = dns.resolver.resolve(query, 'A')
            # If an A record is returned, the domain is likely listed
            # The specific IP returned often indicates the reason for listing.
            for rdata in answers:
                listed_on.append(f"{dnsbl} (Returned IP: {rdata.address})")
        except dns.resolver.NXDOMAIN:
            # Domain not found on this blacklist
            pass
        except dns.resolver.Timeout:
            print(f"Timeout querying {dnsbl}")
            pass
        except Exception as e:
            print(f"Error querying {dnsbl}: {e}")
            pass

    return listed_on


def generate_verification_token(email):
    """
    Generate verification token for verifying email. Includes a timestamp.
    """
    f = Fernet(os.environ['EMAIL_VERIFICATION_KEY'])
    token = f.encrypt_at_time(email.encode(), int(time.time()))
    return token.decode()


def decrypt_verification_token(token, expiry_sec):
    """
    Decrypt verification token. Verifies that it is not expired (30 min TTL).
    Raises InvalidToken if expired or tampered.
    """
    f = Fernet(os.environ['EMAIL_VERIFICATION_KEY'])
    try:
        email = f.decrypt(token.encode(), ttl=expiry_sec)
        return email.decode()
    except InvalidToken:
        raise ValueError("Token is invalid or has expired.")


def get_hubspot_properties(integration: HubspotIntegration):
    """Fetch all available HubSpot contact properties (columns)."""

    url = "https://api.hubapi.com/crm/v3/properties/contacts"
    headers = {"Authorization": f"Bearer {integration.access_token}"}

    try:
        res = requests.get(url, headers=headers)
        res.raise_for_status()
    except HTTPError as e:
        if res.status_code == 401:
            new_token = refresh_hubspot_access_token(integration)
            headers["Authorization"] = f"Bearer {new_token}"
            res = requests.get(url, headers=headers)
            res.raise_for_status()
        else:
            raise e

    return [p["name"] for p in res.json().get("results", [])]


def fetch_all_contacts(integration: HubspotIntegration, properties):
    """Fetch all contacts from HubSpot with pagination."""
    url = f"https://api.hubapi.com/crm/v3/objects/contacts"
    headers = {"Authorization": f"Bearer {integration.access_token}"}
    params = {
        "limit": 100,
        "properties": ",".join(properties)
    }
    results = []
    while True:
        res = requests.get(url, headers=headers, params=params)

        if res.status_code == 401:
            new_token = refresh_hubspot_access_token(integration)
            headers["Authorization"] = f"Bearer {new_token}"
            res = requests.get(url, headers=headers, params=params)

        res.raise_for_status()
        data = res.json()
        results.extend(data.get("results", []))
        if "paging" in data and "next" in data["paging"]:
            params["after"] = data["paging"]["next"]["after"]
        else:
            break
    return results


def refresh_hubspot_access_token(integration: HubspotIntegration) -> str:
    """
    Refresh HubSpot access token using the refresh token.
    Updates both access_token and refresh_token in the DB.
    """
    payload = {
        "grant_type": "refresh_token",
        "client_id": os.environ['CE_HUBSPOT_CLIENT_ID'],
        "client_secret": os.environ['CE_HUBSPOT_CLIENT_SECRET'],
        "refresh_token": integration.refresh_token,
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    res = requests.post("https://api.hubapi.com/oauth/v1/token", data=payload, headers=headers)
    res.raise_for_status()
    data = res.json()

    integration.access_token = data["access_token"]
    if "refresh_token" in data:
        integration.refresh_token = data["refresh_token"]
    integration.save(update_fields=["access_token", "refresh_token"])

    return data["access_token"]


def fetch_contacts_from_list(integration: HubspotIntegration, properties, list_id: str):
    """Fetch contacts from a specific HubSpot list with pagination (legacy API)."""
    url = f"https://api.hubapi.com/contacts/v1/lists/{list_id}/contacts/all"
    headers = {"Authorization": f"Bearer {integration.access_token}"}
    params = {
        "count": 100,
        "property": properties,
    }

    results = []
    while True:
        res = requests.get(url, headers=headers, params=params)
        if res.status_code == 401:
            new_token = refresh_hubspot_access_token(integration)
            headers["Authorization"] = f"Bearer {new_token}"
            res = requests.get(url, headers=headers, params=params)

        res.raise_for_status()
        data = res.json()

        results.extend(data.get("contacts", []))

        if "has-more" in data and data["has-more"]:
            params["vidOffset"] = data["vid-offset"]
        else:
            break

    normalized = []
    for contact in results:
        props = {}
        for k, v in contact.get("properties", {}).items():
            props[k] = v.get("value")
        normalized.append({"properties": props})

    return normalized


@retry(Exception, tries=3, delay=3)
def get_warmup_emails() -> List[str]:
    """
    Fetches current list of warmup email addresses from our warmup system api.
    """
    try:
        res = requests.get(
            url="https://warmup.deliveryman.ai/get-all-warmup-emails/",
            headers={
                "Content-Type": "application/json",
                "X-API-Key": os.environ["CE_EMAIL_WARMUP_API_KEY"],
            },
            timeout=10,
        )
        if res.status_code == 200:
            emails: List[str] = res.json()["emails"]
            return emails

        else:
            print(res.content)
            raise Exception(f"get_warmup_emails() failed with status {res.status_code}")

    except Exception as err:
        raise Exception(f"get_warmup_emails() API call failed: {err}")


def csv_generator(contacts, schedules, bad_emails, bad_reason_map, unsubscribed, attribute_keys):
    '''
    csv generator
    '''
    class Echo:
        def write(self, value):
            return value

    writer = csv.writer(Echo())
    header =  attribute_keys + ["email_id", "status", "sent_on", "reply", "bad_email", "unsubscribed", "reason"]
    yield writer.writerow(header)

    for c in contacts:
        schedule = schedules.get(c.id)
        attr_values = [c.attributes.get(k, "") for k in attribute_keys]
        row = attr_values + [
            c.email_id,
            schedule.status if schedule else "Not Scheduled",
            schedule.sent_on.strftime("%Y-%m-%d %H:%M") if schedule and schedule.sent_on else "",
            schedule.reply_classification if schedule else "",
            "Yes" if c.email_id in bad_emails else "No",
            "Yes" if c.email_id in unsubscribed else "No",
            bad_reason_map.get(c.email_id, "") if c.email_id in bad_emails else "",
        ]
        yield writer.writerow(row)
