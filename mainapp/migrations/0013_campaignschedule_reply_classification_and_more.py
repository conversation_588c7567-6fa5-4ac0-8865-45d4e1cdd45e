# Generated by Django 5.1.2 on 2025-01-07 10:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0012_campaignignoredcontacts'),
    ]

    operations = [
        migrations.AddField(
            model_name='campaignschedule',
            name='reply_classification',
            field=models.CharField(choices=[('positive', 'Positive'), ('neutral', 'Neutral'), ('negative', 'Negative')], db_index=True, default=None, max_length=100, null=True),
        ),
        migrations.CreateModel(
            name='CampaignActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_subject', models.TextField()),
                ('event_from', models.TextField()),
                ('event_date', models.DateTimeField(auto_now_add=True)),
                ('event_additional_data', models.JSONField(default=dict)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mainapp.campaign')),
            ],
        ),
    ]
