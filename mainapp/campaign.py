import json
import logging
import os
import random
import re
import time
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from itertools import batched
from typing import List, Literal, Dict

import botocore
import botocore.exceptions
from dateutil.relativedelta import relativedelta
from django.db import IntegrityError
from django.utils import timezone
from unidecode import unidecode

from ColdEmailerBackend.settings import aws_eventbridge, DEBUG
from mainapp.campaign_schedule_generators import PatternFollowingScheduleGenerator
from mainapp.models import Campaign, EmailID, CampaignContact, CampaignSchedule, CampaignEmailMessage, \
    UnsubscribedEmail, CampaignActivity
from mainapp.pydantic_models import SaveCampaignMessageSchema, Schedule, EmailContentCheck
from mainapp.spam_rate_score import SpamRateScore
from mainapp.utils import check_email_contents

if DEBUG:
    logger = logging.getLogger("dev")
else:
    logger = logging.getLogger("gunicorn.error")


# ------------------------- CUSTOM EXCEPTIONS -------------------------

class CampaignNotFound(Exception):
    def __init__(self, campaign_id: str):
        self.campaign_id = campaign_id
        super().__init__(f"No campaign found with id: {campaign_id}")


class CampaignSaveFailed(Exception):
    def __init__(self, campaign_id: str, message: str):
        self.campaign_id = campaign_id
        super().__init__(f"Failed: {message}")


class CampaignStartException(Exception):
    def __init__(self, campaign_id: str, message: str):
        self.campaign_id = campaign_id
        super().__init__(message)


class CampaignResumeException(Exception):
    def __init__(self, campaign_id: str, message: str):
        self.campaign_id = campaign_id
        super().__init__(message)


class CampaignNotEnoughEmails(Exception):
    def __init__(self, campaign_id: str, message: str):
        self.campaign_id = campaign_id
        super().__init__(f"Campaign {campaign_id} does not have enough emails: {message}")


class CampaignCancelError(Exception):
    def __init__(self, campaign_id: str, message: str):
        self.campaign_id = campaign_id
        super().__init__(f"Campaign {campaign_id} cannot be cancelled: {message}")


class CampaignContactNotFound(Exception):
    def __init__(self, campaign_id: str, email_id: str):
        self.campaign_id = campaign_id
        self.email_id = email_id
        super().__init__(f"Could not find contact with email id {email_id} in campaign {campaign_id}")


# ----------------------------------------------------------------------


class CampaignManager:
    def __init__(self, campaign: str | Campaign):
        """
        :param campaign: Can be either campaign model object (Campaign) or the campaign uid value (string).
        """
        if type(campaign) is str:
            self._campaign_uid: str = campaign
            try:
                self._campaign: Campaign = Campaign.objects.get(uid=campaign)
            except Campaign.DoesNotExist:
                raise CampaignNotFound(campaign)
        else:
            self._campaign = campaign
            self._campaign_uid = campaign.uid

        self._user_id: int = self._campaign.workspace.user.id

    def save_email_messages(self, new_email_data: List[SaveCampaignMessageSchema]):
        """
        Validates and saves changes using given email data.

        :param new_email_data: List of email data dictionaries
         {uid: str, subject: str, body: str, next_message_days: int, sent: bool, sent_on_ts: int | None}
        """
        if self._campaign.status != "created":
            raise CampaignSaveFailed(campaign_id=self._campaign_uid, message="Emails cannot be edited in current "
                                                                             "campaign state.")

        # Validate the email data.
        for email in new_email_data:
            if not email.uid:
                raise CampaignSaveFailed(self._campaign_uid, "Missing UID. Please inform us about this "
                                                             "issue.")

            if not (email.subject and email.body):
                raise CampaignSaveFailed(self._campaign_uid, "One or more subject/body of your email is "
                                                             "empty. Please fix this issue and try again.")

            if email.next_message_days < 1:
                raise CampaignSaveFailed(self._campaign_uid, "Next message days must be greater than or "
                                                             "equal to 1.")

            if email.content_type not in ["text/plain", "text/html"]:
                raise CampaignSaveFailed(self._campaign_uid, f"Unknown content type '{email.content_type}'")

#         # Run through content filtering.
#         for data in new_email_data:
#             filter_result: EmailContentCheck = check_email_contents(subject=data.subject, body=data.body)
#             if filter_result.reject:
#                 raise CampaignSaveFailed(
#                     campaign_id=self._campaign_uid,
#                     message=f"""One or more of your email subject/body triggered our content check filters.
# Reason: {filter_result.reason}
# Trigger Words: {', '.join(filter_result.trigger_words)}"""
#                 )

        # Delete old data.
        self._campaign.campaignemailmessage_set.all().delete()

        # Replace existing data with new one.
        for index, data in enumerate(new_email_data):
            # Run this through unidecode to fix any font issues.
            cleaned_body_text: str = unidecode(data.body)

            # Calculate spam rate score.
            srs = SpamRateScore()
            # No need to handle ValueError since we have already checked for subject & body contents.
            result = srs.score_email(data.subject, data.body)

            # Run content filtering
            filter_result: EmailContentCheck = check_email_contents(subject=data.subject, body=data.body)
            if filter_result.reject:
                label = "blocked"
                reason = filter_result.reason
            else:
                label = "passed"
                reason = None

            CampaignEmailMessage.objects.create(
                campaign=self._campaign,
                order=index,
                uid=data.uid,
                subject=data.subject,
                body=cleaned_body_text,
                next_message_days=data.next_message_days,

                score=result.score,
                verdict=result.verdict,
                suggestions=result.suggestions,

                label=label,
                blocked_reason=reason,

                email_content_type=data.content_type,

                spintax_version="v2",
            )

        logger.debug(f"All changes saved for {self._campaign_uid}!")

    def start_campaign(self):
        """
        Starts this campaign by creating emails and schedule. Avoid calling this directly. Use it from celery task.

        :raises: CampaignNotEnoughEmails exception if not enough sending emails are avialable.
        """
        self._campaign.is_generating_schedules = True
        self._campaign.save()

        # Create the schedules.
        logger.debug("Generating schedules...")
        timer = time.time()
        pf_gen = PatternFollowingScheduleGenerator(
            campaign=self._campaign,
        )
        schedules: List[Schedule] = pf_gen.generate_schedules(days_to_skip=self._campaign.skip_days)
        logger.debug(f"PatternFollowingScheduleGenerator took {time.time() - timer:.2f} seconds")

        timer = time.time()
        # --- Step 1: Collect all necessary IDs from the initial list ---
        # This avoids iterating over `schedules` multiple times.
        sending_email_ids = set()
        contact_ids = set()
        message_ids = set()
        for s in schedules:
            sending_email_ids.add(s.sending_email_id)
            contact_ids.add(s.contact_id)
            message_ids.add(s.message_id)

        # --- Step 2: Fetch all required objects from the database in bulk ---
        emails_by_id = EmailID.objects.in_bulk(list(sending_email_ids))
        contacts_by_id = self._campaign.campaigncontact_set.in_bulk(list(contact_ids))
        messages_by_id = self._campaign.campaignemailmessage_set.in_bulk(list(message_ids))

        # --- Step 3: Process data in memory and prepare for bulk operations ---
        # To keep it fast, this loop performs NO database queries.
        contacts_to_update = []
        bulk_campaign_schedule_creation_tasks = []
        logger.debug(f"Data prefetching took {time.time() - timer:.2f} seconds")

        if len(schedules) == 0:
            raise Exception("No schedules were generated by PatternFollowingScheduleGenerator.")

        # Over here we'll build the bulk database ops for CampaignContact updates and CampaignSchedule creation.
        timer = time.time()
        for schedule in schedules:
            # Fetch objects from our pre-filled dictionaries.
            sending_email = emails_by_id.get(schedule.sending_email_id)
            contact = contacts_by_id.get(schedule.contact_id)
            message = messages_by_id.get(schedule.message_id)

            # # Skip if any related object was not found (uncomment this code if required)
            # if not all([sending_email, contact, message]):
            #     logging.warning(f"Skipping schedule due to missing related object for contact {schedule.contact_id}")
            #     continue

            contact.sending_email = sending_email
            contacts_to_update.append(contact)

            bulk_campaign_schedule_creation_tasks.append(CampaignSchedule(
                uid=f"campaign_contact_email_{uuid.uuid4().hex}",
                campaign=self._campaign,
                contact=contact,
                email_message=message,
                schedule_name=f"campaign_email_{uuid.uuid4().hex}",
                schedule_datetime=schedule.date,
            ))
        logger.debug(f"For loop took {time.time() - timer:.2f} seconds")

        # --- Step 4: Perform bulk database writes ---
        timer = time.time()
        for batch in batched(contacts_to_update, 10000):
            CampaignContact.objects.bulk_update(batch, fields=['sending_email'], batch_size=5000)
        logger.debug(f"Contact bulk update took {time.time() - timer:.2f} seconds")

        timer = time.time()
        CampaignSchedule.objects.bulk_create(bulk_campaign_schedule_creation_tasks, batch_size=5000)
        logger.debug(f"CampaignSchedule bulk create took {time.time() - timer:.2f} seconds")

        # --- Step 5: Bulk create next 1 hour schedules on aws.
        timer = time.time()
        current_time = timezone.now()
        hour_end = current_time + relativedelta(hours=+1)
        self._campaign.refresh_from_db()
        first_hour_schedules = []
        # schedule.contact.sending_email.email_subdomain.managed_subdomain.config_set_name,
        for schedule in self._campaign.campaignschedule_set.prefetch_related(
                "contact__sending_email__email_subdomain__managed_subdomain"
        ).filter(
                schedule_datetime__gte=current_time,
                schedule_datetime__lte=hour_end,
        ):
            first_hour_schedules.append(schedule)
        self.bulk_create_aws_schedules(first_hour_schedules)
        logger.debug(f"bulk_create_aws_schedules took {time.time() - timer}:2f seconds")

        # --- Step 6: Update the status for scheduled emails.
        for schedule in first_hour_schedules:
            schedule.status = "scheduled"

        CampaignSchedule.objects.bulk_update(first_hour_schedules, fields=['status'], batch_size=100)

        self._campaign.is_generating_schedules = False
        self._campaign.save()

    def pause_campaign(self):
        """
        Pauses this campaign. Avoid calling this directly. Use it from celery task.
        """
        logger.debug(f"Pausing campaign {self._campaign_uid}")

        self._campaign.is_deleting_schedules = True
        self._campaign.save()

        # Delete all remaining schedules.
        schedule_names: List[str] = list(
            self._campaign.campaignschedule_set.filter(
                status__in=["scheduled"]
            ).values_list(
                "schedule_name",
                flat=True
            )
        )
        self._bulk_delete_schedules(schedule_names)

        # Change all "scheduled" status into "created"
        schedule_updates: List[CampaignSchedule] = list(self._campaign.campaignschedule_set.filter(
            status="scheduled"
        ))
        for schedule in schedule_updates:
            schedule.status = "created"
        CampaignSchedule.objects.bulk_update(schedule_updates, fields=['status'], batch_size=100)

        self._campaign.status = "paused"
        self._campaign.campaign_paused = True
        self._campaign.campaign_paused_on = timezone.now()
        self._campaign.is_deleting_schedules = False
        self._campaign.save()

        logger.info(f"Campaign {self._campaign_uid} has been paused")

    def resume_campaign(self):
        self._campaign.is_generating_schedules = True
        self._campaign.save()

        # We need to update all schedule times by calculating the time difference between when it was paused
        # and when it resumed, and adding 15 mins extra.
        resumed_on = timezone.now()
        time_difference_in_seconds: int = (resumed_on - self._campaign.campaign_paused_on).total_seconds() + (15 * 60)

        # Update all "created" status schedule times.
        updated_schedules: List[CampaignSchedule] = []
        for schedule in self._campaign.campaignschedule_set.filter(status__in=["created"]):
            schedule.schedule_datetime = schedule.schedule_datetime + relativedelta(seconds=time_difference_in_seconds)
            updated_schedules.append(schedule)

        logger.info("Bulk updating schedule dates...")
        start = time.time()
        CampaignSchedule.objects.bulk_update(updated_schedules, ["schedule_datetime"], batch_size=5000)
        logger.debug(f"Bulk update took {time.time() - start:.2f} seconds")

        # Bulk create all schedules for next 1 hour on aws.
        logger.info("Recreating next hour aws schedules...")
        current_time = timezone.now()
        hour_end = current_time + relativedelta(hours=+1)
        self._campaign.refresh_from_db()
        first_hour_schedules = list(self._campaign.campaignschedule_set.prefetch_related(
                "contact__sending_email__email_subdomain__managed_subdomain"
        ).filter(
            status="created",
            schedule_datetime__gte=current_time,
            schedule_datetime__lte=hour_end,
        ))

        logger.info(f"Need to create {len(first_hour_schedules)} schedules on AWS")
        self.bulk_create_aws_schedules(first_hour_schedules)

        # Update their status.
        for schedule in first_hour_schedules:
            schedule.status = "scheduled"

        CampaignSchedule.objects.bulk_update(first_hour_schedules, fields=['status'], batch_size=100)

        self._campaign.status = "running"
        self._campaign.campaign_paused = False
        self._campaign.campaign_paused_on = None
        self._campaign.is_generating_schedules = False
        self._campaign.save()

        logger.info(f"Campaign {self._campaign_uid} has been resumed")

    def stop_campaign(self):
        """
        Cancels this campaign. Avoid calling this directly. Use it from celery task.
        """
        self._campaign.is_deleting_schedules = True
        self._campaign.save()

        # Delete all schedules on aws.
        schedule_names: List[str] = list(
            self._campaign.campaignschedule_set.filter(
                status__in=["scheduled"]
            ).values_list(
                "schedule_name",
                flat=True
            )
        )
        self._bulk_delete_schedules(schedule_names)

        # Update all schedule status.
        self._campaign.campaignschedule_set.filter(status__in=["created"]).update(status="cancelled_campaign_stopped")

        # Close all contacts.
        self._campaign.campaigncontact_set.update(active=False)

        # Mark this campaign as cancelled.
        self._campaign.is_deleting_schedules = False
        self._campaign.scheduled_start_datetime = None
        self._campaign.status = "cancelled"
        self._campaign.save()

    @staticmethod
    def create_aws_schedule(
            schedule_uid: str,
            schedule_name: str,
            schedule_expression: str,
            recipient_email_id: str,
            subject: str,
            body: str,
            content_type: str,
            fbl_campaign_id: str,
            fbl_user_id: str,
            fbl_sender_id: str,
            sender_email_id: str,
            sender_name: str,
            unubscribe_value: str,
            previous_message_id: str | None,
            config_set_name: str | None,
    ):
        """
        Creates a single AWS EventBridge schedule.

        :param schedule_uid: CampaignSchedule model object uid.
        :param schedule_name: CampaignSchedule model object name.
        :param schedule_expression: Schedule datetime string for AWS EventBridge schedule.
        :param recipient_email_id: Recipient email address.
        :param subject: Subject string.
        :param body: Body string.
        :param content_type: Content type of email message (text/plain or text/html).
        :param fbl_campaign_id: Campaign id for feedback loop.
        :param fbl_user_id: User id for feedback loop.
        :param fbl_sender_id: Feedback loop sender id.
        :param sender_email_id: Sender email address.
        :param sender_name: Sender email name.
        :param unubscribe_value: Unsubscribe header string.
        :param previous_message_id: First email Message-Id header value if this is a followup message (for message threading).
        :param config_set_name: SES config set name for tracking. Use None if config set is not available.
        """
        aws_eventbridge.create_schedule(
            ActionAfterCompletion="DELETE",
            FlexibleTimeWindow={
                "Mode": "OFF"
            },
            Name=schedule_name,
            ScheduleExpression=schedule_expression,
            ScheduleExpressionTimezone="UTC",
            State="ENABLED",
            Target={
                "Arn": os.environ["CE_SEND_EMAIL_LAMBDA_ARN"],
                "Input": json.dumps({
                    "uid": schedule_uid,
                    "recipient": recipient_email_id,
                    "subject": subject,
                    "body": body,
                    "content_type": content_type,
                    "sender_email": sender_email_id,
                    "sender_name": sender_name,
                    "list_unsubscribe_value": unubscribe_value,
                    "fbl_sender_id": fbl_sender_id,
                    "fbl_campaign_id": fbl_campaign_id,
                    "fbl_user_id": fbl_user_id,
                    "previous_message_id": previous_message_id,
                    "config_set_name": config_set_name,
                }),
                "RetryPolicy": {
                    "MaximumRetryAttempts": 3
                },
                "RoleArn": os.environ["CE_EVENTBRIDGE_ROLE_ARN"],
            },
        )

    @staticmethod
    def _delete_schedule(schedule_name: str):
        """

        :param schedule_name:
        :return:
        """
        try:
            aws_eventbridge.delete_schedule(
                Name=schedule_name,
            )
        except Exception as err:
            logger.debug(f"{err}")

    def _bulk_delete_schedules(self, schedule_names: List[str], max_workers=100):
        """

        :param schedule_names:
        :param max_workers:
        :return:
        """
        futures = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            for name in schedule_names:
                futures.append(executor.submit(
                    self._delete_schedule,
                    name,
                ))

            for fututre in futures:
                fututre.result()

    @staticmethod
    def variable_substitution(text: str, variables: Dict):
        """
        Replaces all email variables in given text.
        """
        substituted_text: str = text
        for var in variables.keys():
            substituted_text = substituted_text.replace("{{" + var.strip() + "}}", variables[var])

        return substituted_text

    @staticmethod
    def spintax_substitution(text: str, version: str):
        """
        Performs spintax substitution.

        Spintax is a random combination of words and phrases to create a variety of the same email. It takes the
        spintax tag {|word1|word2|word3|...|} and replaces it with a random word from given ones, througout the text.
        """
        # We'll keep the updated copy of text here.
        new_text: str = text

        if version == "v1":
            # We'll use regex to find and iterate through all {|word1|word2|word3|...|} matches.
            matches = re.finditer(r"{\|(.*?)\|}", text)
        else:
            # We'll use regex to find and iterate through all {word1|word2|word3|...} matches.
            matches = re.finditer(r"{(.*?)}", text)

        for match in matches:
            tag: str = match.group(0)

            # Generate list of words.
            if version == "v1":
                words: List[str] = tag.lstrip("{|").rstrip("|}").split("|")
            else:
                words: List[str] = tag.lstrip("{").rstrip("}").split("|")

            # Remove any leading/trailing spaces from the words.
            words = [word.strip() for word in words]

            # Replace the tag with a random word from the list.
            new_text = new_text.replace(tag, random.choice(words), 1)

        return new_text

    @staticmethod
    def sender_name_substitution(text: str, name: str):
        """
        Replaces all sender name variables in given text.
        """
        substituted_text: str = text
        substituted_text = substituted_text.replace("[sender]", name)

        return substituted_text

    @staticmethod
    def bulk_create_aws_schedules(schedules: List[CampaignSchedule], max_workers=100):
        """
        Creates AWS EventBridge schedules in bulk.

        :param schedules: List of CampaignSchedule objects to use for creating schedules.
        :param max_workers: Worker threads count.
        """
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            for schedule in schedules:
                if schedule.status == "created":
                    # Substitute variables in email message with their values.
                    subject_text = CampaignManager.variable_substitution(
                        schedule.email_message.subject,
                        schedule.contact.attributes
                    )
                    body_text = CampaignManager.variable_substitution(
                        schedule.email_message.body,
                        schedule.contact.attributes
                    )
                    body_text = CampaignManager.sender_name_substitution(
                        body_text,
                        schedule.contact.sending_email.username,
                    )

                    # Add unsub link to end of body if required.
                    if schedule.campaign.add_unsub_link:
                        if schedule.campaign.unsub_link_type == "custom":
                            body_text += f"\n\n{schedule.campaign.custom_unsub_text} \"Unsubscribe\""
                        else:
                            unsub_texts = [
                                "If you’d prefer not to hear from me again, just reply with \"Unsubscribe\".",
                                "Don’t want emails like this? Reply \"Unsubscribe\".",
                                "If this isn’t relevant, feel free to reply with \"Unsubscribe\".",
                                "Not the right fit? Just reply with \"Unsubscribe\".",
                                "Want to opt out? Just reply with \"Unsubscribe\".",
                                "No longer interested? Just reply with \"Unsubscribe\".",
                                "To stop receiving my emails, reply with \"Unsubscribe\".",
                            ]
                            body_text += f"\n\n{random.choice(unsub_texts)}"

                    # Perform spintax substitution.
                    # subject_text = CampaignManager.spintax_substitution(subject_text, schedule.email_message.spintax_version)
                    body_text = CampaignManager.spintax_substitution(body_text, schedule.email_message.spintax_version)

                    # Generate unsubscibe header value.
                    unsub_link: str = os.environ["CE_UNSUBSCRIBE_LINK"] + (f"?cid={schedule.campaign.uid}"
                                                                           f"&email={schedule.contact.email_id}")
                    unsub_header_value: str = f"<{unsub_link}>"

                    # If this is a followup message (message order > 0) then we need to add References header.
                    # For that we'll fetch the Message-Id value of the first message (message order == 0).
                    previous_message_id: str | None = None
                    if schedule.email_message.order == 0:
                        # First email in sequence for this contact
                        if not schedule.resolved_subject:
                            schedule.resolved_subject = CampaignManager.spintax_substitution(
                                subject_text,
                                schedule.email_message.spintax_version
                            )
                            schedule.save(update_fields=["resolved_subject"])
                        subject_text = schedule.resolved_subject                        
                    elif schedule.email_message.order > 0:
                        try:
                            first_message: CampaignSchedule = schedule.campaign.campaignschedule_set.get(
                                contact__email_id=schedule.contact.email_id,
                                email_message__order=0
                            )
                            previous_message_id = first_message.message_id
                            
                            if first_message:
                                subject_text = first_message.resolved_subject
                            else:
                                # fallback – should rarely happen
                                subject_text = CampaignManager.spintax_substitution(
                                    subject_text,
                                    schedule.email_message.spintax_version
                                )                                                          
                        except CampaignSchedule.DoesNotExist:
                            logger.critical(f"Failed to find first message for schedule id {schedule.id} belonging "
                                            f"to campaign {schedule.campaign.uid}")

                    # Add the task.
                    futures.append(executor.submit(
                        CampaignManager.create_aws_schedule,
                        schedule.uid,
                        schedule.schedule_name,
                        f"at({schedule.schedule_datetime.strftime('%Y-%m-%dT%H:%M:%S')})",
                        schedule.contact.email_id,
                        subject_text,
                        body_text,
                        schedule.email_message.email_content_type,
                        f"campaign-{schedule.campaign.id}",
                        f"user-{schedule.campaign.workspace.user.id}",
                        f"emailid-{schedule.contact.sending_email.id}",
                        schedule.contact.sending_email.email_address,
                        schedule.contact.sending_email.username,
                        unsub_header_value,
                        previous_message_id,
                        schedule.contact.sending_email.email_subdomain.managed_subdomain.config_set_name,
                    ))

            for future in futures:
                future.result()

    def unsubscribe_contact(self, email_id: str):
        """
        Use to unsubscribe user from this and future campaigns.

        :param email_id: Contact email id to unsubscribe.
        """
        logger.debug(f"Unsubscribing {email_id} from campaign {self._campaign.name}...")

        # Fetch the contact.
        try:
            contact = self._campaign.campaigncontact_set.get(email_id=email_id)
        except CampaignContact.DoesNotExist:
            raise CampaignContactNotFound(campaign_id=self._campaign_uid, email_id=email_id)

        # Mark the contact as inactive.
        contact.active = False
        contact.save()

        # Mark contact list contact as unsubscribed.
        if contact.imported_contact_list_contact:
            contact.imported_contact_list_contact.unsubscribed = True
            contact.imported_contact_list_contact.save()

        # Cancel all remaining schedules.
        for schedule in contact.campaignschedule_set.filter(status__in=["created", "scheduled", "sent"]):
            # Delete schedule.
            try:
                aws_eventbridge.delete_schedule(
                    Name=schedule.schedule_name,
                )

            except botocore.exceptions.ClientError as err:
                # Only skip the error if it's due to missing schedule.
                if err.response['Error']['Code'] != "ResourceNotFoundException":
                    raise err

            # Update status.
            schedule.status = "cancelled_unsubscribed"
            schedule.save()

            logger.debug(f"Schedule {schedule.schedule_name} has been deleted successfully.")

        # Mark this user in unsubscribed list.
        try:
            UnsubscribedEmail.objects.create(
                workspace=self._campaign.workspace,
                email_id=contact.email_id
            )
            logger.debug(f"{contact.email_id} has been unsubscribed.")
        except IntegrityError:
            # Ignore if already added
            logger.debug(f"{contact.email_id} already unsubscribed.")

        # Create an activity.
        CampaignActivity.objects.create(
            campaign=self._campaign,
            event_type="unsubscribe",
            event_subject="Unsubscribe Request",
            event_from=email_id,
        )

    def end_campaign_for_contact__reply_received(self,
                                                 schedule: CampaignSchedule,
                                                 reply_class: Literal["positive", "neutral", "negative"],
                                                 email_s3_key: str):
        """
        Use to end campaign for given contact. If reply class is "negative", it will also unsubscribe this
        schedule contact email.

        Also creates camapign activity log for reply and unsubscribed.

        :param schedule: CampaignSchedule object to end campaign.
        :param reply_class: reply classification result - "positive", "neutral" or  "negative".
        :param email_s3_key: AWS S3 object name where this email reply is stored.
        """
        # Create activity for this event.
        if reply_class == "positive":
            event_subject = "Positive Reply"

        elif reply_class == "negative":
            event_subject = "Negative Reply"

        else:
            event_subject = "Neutral Reply"

        CampaignActivity.objects.create(
            campaign=self._campaign,
            event_subject=event_subject,
            event_from=schedule.contact.email_id,
            event_additional_data={"email_s3_key": email_s3_key}
        )

        if reply_class in ["positive", "neutral"]:
            schedule.reply_received = True
            schedule.status = "replied"
            schedule.reply_classification = reply_class
            schedule.email_s3_key = email_s3_key
            schedule.save()

            schedule.contact.active = False
            schedule.contact.save()

            # Cancel all remaining schedules for this contact.
            for schedule in schedule.contact.campaignschedule_set.filter(status="created"):
                try:
                    aws_eventbridge.delete_schedule(
                        Name=schedule.schedule_name,
                    )
                except botocore.exceptions.ClientError as err:
                    # Only skip the error if it's due to missing schedule.
                    if err.response['Error']['Code'] != "ResourceNotFoundException":
                        raise err
                # Update status.
                schedule.status = "cancelled_reply_received"
                schedule.save()

                logger.debug(f"Schedule {schedule.schedule_name} has been deleted successfully.")

        else:
            schedule.reply_received = True
            schedule.status = "cancelled_unsubscribed"
            schedule.reply_classification = reply_class
            schedule.email_s3_key = email_s3_key
            schedule.save()

            schedule.contact.active = False
            schedule.contact.save()

            # Unsubscribe this email id (also creates activity).
            self.unsubscribe_contact(schedule.contact.email_id)

    def resubscribe_contact(self, email_id: str):
        """
        Use to resubscribe user from this and future campaigns.

        :param email_id: Contact email id to resubscribe.
        """
        try:
            contact = self._campaign.campaigncontact_set.get(email_id=email_id)
        except CampaignContact.DoesNotExist:
            raise CampaignContactNotFound(campaign_id=self._campaign_uid, email_id=email_id)

        # Mark the contact as active again
        contact.active = True
        contact.save()

        # Reset contact list contact if present
        if contact.imported_contact_list_contact:
            contact.imported_contact_list_contact.unsubscribed = False
            contact.imported_contact_list_contact.save()

        # Remove from unsubscribed list
        UnsubscribedEmail.objects.filter(
            workspace=self._campaign.workspace,
            email_id=contact.email_id
        ).delete()

        CampaignActivity.objects.create(
            campaign=self._campaign,
            event_type="resubscribe",
            event_subject="Resubscribe Request",
            event_from=email_id,
        )
