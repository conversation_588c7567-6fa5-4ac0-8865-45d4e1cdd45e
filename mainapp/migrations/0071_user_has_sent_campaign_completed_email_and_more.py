# Generated by Django 5.1.2 on 2025-06-28 10:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mainapp', '0070_campaign_add_unsub_link_campaign_custom_unsub_text_and_more'),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name='user',
            name='has_sent_campaign_completed_email',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='has_sent_campaign_started_email',
            field=models.<PERSON>olean<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='has_sent_domain_connected_email',
            field=models.<PERSON>oleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='has_sent_domain_not_connected_email',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='has_sent_domain_warmed_up_email',
            field=models.<PERSON><PERSON>an<PERSON>ield(default=False),
        ),
        migrations.Add<PERSON>ield(
            model_name='user',
            name='has_sent_no_campaign_creation_email',
            field=models.<PERSON>olean<PERSON>ield(default=False),
        ),
    ]
