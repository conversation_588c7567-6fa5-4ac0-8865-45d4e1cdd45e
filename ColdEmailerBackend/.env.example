# Please add CE_ prefix to prevent conflicts.

CE_DEBUG=1

# Leave empty on dev env.
CE_LOG_ERROR_FILE_PATH=

CE_APP_HOST_URL=http://localhost:3000
# Generate using secrets
CE_DJANGO_SECRET_KEY=
# Generate using secrets
CE_JWT_SIGNING_KEY=
# Generate using fernet
CE_PASSWORD_RESET_FERNET_KEY=
# Generate using fernet
EMAIL_VERIFICATION_KEY=
# Generate using fernet
CE_GOOGLE_AUTH_STATE_KEY=
CE_ALLOWED_HOSTS=
CE_CORS_ALLOWED_ORIGINS=
CE_UNSUBSCRIBE_LINK=
CE_REPLY_TO_ADDRESS=<EMAIL>

CE_NEW_SIGNUP_ALERT_EMAILS=
CE_APPROVAL_REQUEST_EMAILS=

CE_EMAIL_WARMUP_API_KEY=
CE_EMAIL_WARMUP_INCREASE=0.1

CE_DOMAIN_REDIRECT_API_KEY=

CE_POSTGRES_HOST=
CE_POSTGRES_PORT=
CE_POSTGRES_USER=
CE_POSTGRES_PASSWORD=
CE_POSTGRES_DB=

CE_REDIS_HOST=
CE_REDIS_PORT=
CE_REDIS_DB=

CE_OPENAI_API_KEY=

# We are not using this atm so you can leave these two blank.
CE_FORWARDEMAIL_AUTH_TOKEN=
CE_FORWARDEMAIL_WEBHOOK=

CE_AWS_REGION=ap-south-1
CE_SEND_EMAIL_LAMBDA_ARN=
CE_EVENTBRIDGE_ROLE_ARN=
CE_REPLY_S3_BUCKET_NAME=
CE_SNS_HTTPS_ENDPOINT_HOST=

CE_CELERY_BROKER=
CE_CELERY_BACKEND=

CE_GOOGLE_API_SERVICE_ACCOUNT_FILE=
CE_GOOGLE_API_CLIENT_SECRET=

CE_CRONITOR_API_KEY=

CE_STRIPE_API_KEY=
CE_STRIPE_WEBHOOK_SECRET=
CE_STRIPE_SUCCESS_URL=
CE_STRIPE_CANCEL_URL=
CE_STRIPE_CUSTOMER_PORTAL_RETURN_URL=
CE_STRIPE_INR_TAX_RATE_ID=
CE_STRIPE_NEW_USER_MONTH_COUPON_ID=
CE_STRIPE_NEW_USER_YEAR_COUPON_ID=

CE_HUBSPOT_CLIENT_ID=
CE_HUBSPOT_CLIENT_SECRET=
CE_HUBSPOT_REDIRECT_URL=
CE_ON_SITE_REDIRECT_URL=
