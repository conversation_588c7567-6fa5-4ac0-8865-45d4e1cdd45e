import os

import boto3

import requests
from retry import retry


def lambda_handler(event, context):
    # Extract event data
    schedule_uid: str = event['uid']
    recipient: str = event['recipient']
    subject: str = event['subject']
    body: str = event['body']
    content_type: str = event.get('content_type', 'text/plain')
    sender_email: str = event['sender_email']
    sender_name: str = event['sender_name']
    list_unsubscribe_value: str = event['list_unsubscribe_value']

    fbl_sender_id: str = event['fbl_sender_id']
    fbl_campaign_id: str = event['fbl_campaign_id']
    fbl_user_id: str = event['fbl_user_id']

    config_set_name: str | None = event.get('config_set_name', None)

    # Fetch previous message id if available.
    if "previous_message_id" in event:
        previous_message_id: str | None = event['previous_message_id']
    else:
        previous_message_id: str | None = None

    # Check for duplicate events.
    status: str = get_email_status(schedule_uid)
    if status != "sent":
        # Initialize SES client
        ses_client = boto3.client('ses')

        # Fetch public IP address. If not found, we will not include the related header.
        try:
            public_ip = requests.get('https://api.ipify.org').text

        except requests.RequestException as err:
            print(f"Error fetching public IP: {err}")
            public_ip = None

        # Email headers
        headers = [
            ("From", f'"{sender_name}" <{sender_email}>'),
            ("To", f'"{recipient}" <{recipient}>'),
            ("Reply-To", sender_email),
            ("Subject", subject),
            ("Content-Type", f"{content_type}; charset=UTF-8"),
            ("Content-Transfer-Encoding", "8bit"),
            ("List-Unsubscribe", list_unsubscribe_value),
            ("List-Unsubscribe-Post", "List-Unsubscribe=One-Click"),
            ("Feedback-ID", f"{fbl_campaign_id}:{fbl_user_id}:campaign:{fbl_sender_id}"),
            ("X-Auto-Response-Suppress", "All"),
            ("X-MSys-Trusted-Sender", sender_email),
            ("X-MSys-Originating-IP", f'[{public_ip or "127.0.0.1"}]'),
        ]

        # If we have message id, use it to continue the thread.
        if previous_message_id:
            headers.append(("References", f'<{previous_message_id}@{os.environ["MESSAGE_ID_DOMAIN"]}>'))

        # Build the message.
        raw_message: str = "\n".join([f"{header[0]}: {header[1]}" for header in headers])
        raw_message += f"\n\n{body}"

        try:
            if config_set_name:
                print(f"Using config set {config_set_name}")
                response = ses_client.send_raw_email(
                    Source=f"{sender_name} <{sender_email}>",
                    Destinations=[recipient],
                    RawMessage={'Data': raw_message},
                    Tags=[
                        {'Name': "schedule_uid", 'Value': schedule_uid},
                    ],
                    ConfigurationSetName=config_set_name,
                )
            else:
                print(f"No config set name was provided")
                response = ses_client.send_raw_email(
                    Source=f"{sender_name} <{sender_email}>",
                    Destinations=[recipient],
                    RawMessage={'Data': raw_message},
                    Tags=[
                        {'Name': "schedule_uid", 'Value': schedule_uid},
                    ]
                )

            message_id: str = response["MessageId"]

            # Mark this email as sent.
            success = update_email_status(schedule_uid, message_id, "success")
            if not success:
                print("Failed to make API request")

        except Exception as err:
            print(f"{err}")
            success = update_email_status(schedule_uid, "", "failed")
            if not success:
                print("Failed to make API request")

    else:
        print("Duplicate event. Skipping.")


@retry(Exception, tries=3, delay=3)
def get_email_status(schedule_uid) -> str:
    print(f"Fetching email status for {schedule_uid}")

    res = requests.get(
        url=os.environ["get_email_status_url"] + f"?uid={schedule_uid}",
        timeout=60,
    )
    if res.status_code == 200:
        status = res.json()["status"]
        print(f"Status: {status}")

    else:
        raise Exception(f"get_email_status failed. Status Code: {res.status_code}")

    return status


def update_email_status(uid: str, message_id: str, status: str) -> bool:
    retries = 3
    success = False
    while retries > 0:
        try:
            res = requests.post(
                url=os.environ["update_email_status_url"],
                json={
                    "uid": uid,
                    "message_id": message_id,
                    "status": status,
                },
                timeout=60,
            )

            if res.status_code == 200:
                success = True
                break

            else:
                print(f"Bad Status: {res.status_code}")
                retries -= 1
                continue

        except Exception as err:
            print(f"{err}")
            retries -= 1
            continue

    return success
